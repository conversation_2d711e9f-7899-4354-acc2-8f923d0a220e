import axios from 'axios';
import { PaperRoll } from '../models/PaperRoll';
import { API_URL } from '../config';

export const getPaperRollsForCalculator = async (): Promise<PaperRoll[]> => {
  try {
    const response = await axios.get(`${API_URL}/paper-rolls/`);
    return response.data;
  } catch (error) {
    console.error('Error fetching paper rolls for calculator:', error);
    return [];
  }
};

export interface CalculationInput {
  machine_model: string;
  page_width: number;
  page_height: number;
  pages: number;
  is_duplex: boolean;
  coverage: number;
  is_color: boolean;
  copies: number;
  paper_roll_id?: string | null;
}

export interface CalculationResult {
  linear_meters_per_copy: number;
  total_linear_meters: number;
  products_per_width: number;
  speed_meters_per_minute: number;
  print_time_minutes_per_copy: number;
  total_print_time_minutes: number;
  copies: number;
  total_pages: number;
  paper_roll_id?: string | null;
  paper_roll_supplier?: string | null;
  paper_roll_quality?: string | null;
  paper_roll_width?: number | null;
  paper_roll_grammage?: number | null;
  paper_cost?: number | null;
  ink_cost_per_copy?: number | null;
  total_ink_cost?: number | null;
  ink_cost_per_page?: number | null;
  ink_grams_per_copy?: number | null;
  total_ink_grams?: number | null;
}

export const calculateProduction = async (data: CalculationInput): Promise<CalculationResult> => {
  try {
    // Try test endpoint first, fallback to main endpoint
    let response;
    try {
      response = await axios.post(`${API_URL}/test-auth/test-calculate`, data);
      console.log('Using test calculate endpoint');
    } catch (testError) {
      console.log('Test endpoint failed, trying main endpoint');
      response = await axios.post(`${API_URL}/calculate`, data);
    }
    return response.data;
  } catch (error: unknown) {
    console.error('Error calculating production:', error);
    if (error instanceof Error) {
      throw error;
    } else if (axios.isAxiosError(error) && error.response) {
      throw new Error(error.response.data?.detail || 'Error calculating production');
    } else {
      throw new Error('Error calculating production');
    }
  }
};
