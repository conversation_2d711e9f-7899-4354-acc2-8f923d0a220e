from fastapi import APIRout<PERSON>, HTTPException, Depends
from pydantic import BaseModel, field_validator
from typing import Optional
import math

from services.paper_roll_service import PaperRollService

# Constants
MACHINE_MODELS = ["440", "660", "880"]
MAX_SPEED_METERS_PER_MINUTE = 100
MARGIN_MM = 5  # 5mm margin on each side

# Servicios
from services.machine_config_service import MachineConfigService

# Data models
class CalculationInput(BaseModel):
    machine_model: str
    page_width: float
    page_height: float
    pages: int
    is_duplex: bool
    coverage: float
    is_color: bool
    copies: int
    paper_roll_id: Optional[str] = None
    
    @field_validator('machine_model')
    def validate_machine_model(cls, v):
        if v not in MACHINE_MODELS:
            raise ValueError(f"Invalid machine model. Must be one of {MACHINE_MODELS}")
        return v
    
    @field_validator('coverage')
    def validate_coverage(cls, v):
        if v < 0 or v > 400:
            raise ValueError("Coverage must be between 0 and 400 percent")
        return v

class CalculationOutput(BaseModel):
    linear_meters_per_copy: float
    total_linear_meters: float
    products_per_width: int
    speed_meters_per_minute: float
    print_time_minutes_per_copy: float
    total_print_time_minutes: float
    copies: int
    total_pages: int
    paper_roll_id: Optional[str] = None
    paper_roll_supplier: Optional[str] = None
    paper_roll_quality: Optional[str] = None
    paper_roll_width: Optional[float] = None
    paper_roll_grammage: Optional[int] = None
    paper_cost: Optional[float] = None
    ink_cost_per_copy: Optional[float] = None
    total_ink_cost: Optional[float] = None
    ink_cost_per_page: Optional[float] = None
    ink_grams_per_copy: Optional[float] = None
    total_ink_grams: Optional[float] = None

router = APIRouter(
    tags=["calculator"],
)

# Service instances
def get_paper_roll_service():
    return PaperRollService()

def get_machine_config_service():
    return MachineConfigService()

# Helper function to get effective roll width in mm
def _get_effective_roll_width_mm(data: CalculationInput, paper_roll_service: PaperRollService) -> float:
    if data.paper_roll_id:
        paper_roll = paper_roll_service.get_by_id(data.paper_roll_id)
        if not paper_roll:
            raise HTTPException(
                status_code=404,
                detail=f"Bobina con ID '{data.paper_roll_id}' no encontrada."
            )
        if paper_roll.width is None or paper_roll.width <= 0:
            # Model validation should prevent this, but an extra check.
            raise HTTPException(
                status_code=400,
                detail=f"Bobina con ID '{data.paper_roll_id}' tiene un ancho inválido o no definido."
            )
        return paper_roll.width * 10  # Convert cm (from model) to mm
    else:
        # Fallback to machine model width (already in mm) if no paper roll is selected
        return float(data.machine_model)

# Helper functions
def calculate_products_per_width(data: CalculationInput, paper_roll_service: PaperRollService) -> int:
    actual_roll_width_mm = _get_effective_roll_width_mm(data, paper_roll_service)
    effective_page_width = data.page_width + (2 * MARGIN_MM) # page_width is in mm
    
    if effective_page_width <= 0:
        raise HTTPException(status_code=400, detail="El ancho efectivo de la página debe ser positivo.")

    products_per_width = int(actual_roll_width_mm // effective_page_width)
    return products_per_width

def calculate_linear_meters(data: CalculationInput, paper_roll_service: PaperRollService) -> float:
    # Calculate effective page dimensions including margins (all in mm)
    effective_page_width = data.page_width + (2 * MARGIN_MM)
    effective_page_height = data.page_height + (2 * MARGIN_MM)
    
    # Check if the user has already set the page in landscape orientation
    # If width > height, we assume the user wants landscape orientation
    user_selected_landscape = data.page_width > data.page_height
    
    # Calculate how many products can fit in the width of the machine/roll
    # This call now correctly uses the paper_roll_service to get the actual roll width
    products_per_width = calculate_products_per_width(data, paper_roll_service)
    current_page_orientation_width = effective_page_width
    current_page_orientation_height = effective_page_height

    actual_roll_width_mm = _get_effective_roll_width_mm(data, paper_roll_service)

    # Only try to optimize orientation if the user hasn't already selected landscape
    if not user_selected_landscape:
        # Determine if we can print in landscape orientation to optimize
        # Rotated width is original page height + margins
        rotated_effective_width = data.page_height + (2 * MARGIN_MM) 
        rotated_effective_height = data.page_width + (2 * MARGIN_MM)

        if rotated_effective_width <= actual_roll_width_mm and rotated_effective_width > 0:
            landscape_products_per_width = int(actual_roll_width_mm // rotated_effective_width)
            # Only use landscape if it allows us to fit more products across the width
            # or if it's the only way to fit (e.g. portrait doesn't fit at all)
            if landscape_products_per_width > products_per_width or (products_per_width == 0 and landscape_products_per_width > 0):
                current_page_orientation_width = rotated_effective_width
                current_page_orientation_height = rotated_effective_height
                products_per_width = landscape_products_per_width # Update products_per_width for this orientation
    
    # Calculate total linear meters needed based on the determined page orientation height
    # data.pages is the number of printed sides (as per memory)
    # If duplex, each physical page turn consumes media length equal to one page_height.
    # The number of physical page turns is data.pages / 2 (if duplex and even) or (data.pages+1)/2 (if duplex and odd)
    # which is equivalent to (data.pages + 1) // 2 for duplex.
    # For simplex, it's just data.pages.

    if products_per_width == 0:
         # Cannot fit any product, so linear meters calculation might be problematic or should indicate an error.
         # For now, let's assume this case is caught by validate_page_width.
         # If not, linear_meters_per_product would effectively be infinite or very large.
         # Let's return 0 or raise error, this part of logic needs to be robust.
         # validate_page_width should prevent this for the initial orientation.
         # If landscape was chosen because portrait didn't fit, products_per_width would be > 0.
         # If still 0, it means neither orientation fits.
        pass # Handled by validate_page_width

    num_physical_pages_in_sequence = data.pages
    if data.is_duplex:
        num_physical_pages_in_sequence = (data.pages + 1) // 2 # Each physical page contains up to 2 printed sides

    # Linear meters for one copy is the total height of the sequence of physical pages
    linear_meters_per_product = (current_page_orientation_height * num_physical_pages_in_sequence) / 1000  # Convert mm to meters
    
    return linear_meters_per_product

def calculate_speed(data: CalculationInput) -> float:
    # Calculate printing speed based on coverage
    # Assume that higher coverage reduces speed linearly
    # 0% coverage = MAX_SPEED, 100% coverage = MAX_SPEED/2
    coverage_factor = 1 - (data.coverage / 200)  # 0.5 to 1.0
    
    # Further reduce speed for color printing
    if data.is_color:
        coverage_factor *= 1  # Color printing is slower
    
    speed_meters_per_minute = MAX_SPEED_METERS_PER_MINUTE #* coverage_factor
    return speed_meters_per_minute

def validate_page_width(data: CalculationInput, paper_roll_service: PaperRollService):
    # Use the helper to get products_per_width based on actual roll or machine width
    # This first check is for the default (portrait or user-defined) orientation
    products_per_width_portrait = calculate_products_per_width(data, paper_roll_service)
    actual_roll_width_mm = _get_effective_roll_width_mm(data, paper_roll_service)

    can_fit_portrait = products_per_width_portrait >= 1
    can_fit_landscape = False

    # Check landscape orientation as a fallback if portrait doesn't fit or if specifically checking landscape
    effective_page_width_portrait = data.page_width + (2 * MARGIN_MM)
    
    # Check landscape orientation as a fallback if portrait doesn't fit
    # Rotated width is original page height + margins
    rotated_effective_page_width = data.page_height + (2 * MARGIN_MM)
    if rotated_effective_page_width <= actual_roll_width_mm and rotated_effective_page_width > 0:
        products_per_width_landscape = int(actual_roll_width_mm // rotated_effective_page_width)
        if products_per_width_landscape >=1:
            can_fit_landscape = True

    if not can_fit_portrait and not can_fit_landscape:
        detail_msg = (
            f"El ancho de página ({effective_page_width_portrait:.2f}mm incluyendo márgenes en orientación retrato, "
            f"o {rotated_effective_page_width:.2f}mm en paisaje) excede el ancho útil de la bobina/máquina ({actual_roll_width_mm:.2f}mm)."
        )
        raise HTTPException(
            status_code=400,
            detail=detail_msg
        )
    # No need to return products_per_width from here, it's a validation function.
    # The actual products_per_width for calculation will be determined in calculate_linear_meters

def calculate_ink_usage(data: CalculationInput, linear_meters_per_copy: float, machine_config_service: MachineConfigService) -> tuple[float, float, float, float, float]:
    """Calculate the ink cost and ink consumption per copy and total.
    
    Args:
        data: The calculation input data
        linear_meters_per_copy: The linear meters per copy
        machine_config_service: Service to get machine configuration
        
    Returns:
        Tuple of (ink_cost_per_copy, total_ink_cost, ink_cost_per_page, ink_grams_per_copy, total_ink_grams)
    """
    # Get machine configuration for ink price and consumption
    machine_config = machine_config_service.get_by_model(data.machine_model)
    if not machine_config:
        raise ValueError(f"No se encontró configuración para el modelo {data.machine_model}")
    
    # Calculate the area in m² (convert page dimensions from mm to m)
    page_width_m = data.page_width / 1000
    page_height_m = data.page_height / 1000
    page_area_m2 = page_width_m * page_height_m
    
    # Calculate total area per copy (area per side × number of sides)
    total_area_m2 = page_area_m2 * data.pages
    coverage_percentage = data.coverage / 100  # Convert percentage to fraction

    # Calculate ink grams per copy based on machine's ink consumption
    # ink_consumption is in grams per 1% coverage per m²
    ink_grams_per_copy = total_area_m2 * (coverage_percentage * 100) * machine_config.ink_consumption
    
    # Calculate total ink grams for all copies
    total_ink_grams = ink_grams_per_copy * data.copies
    
    # Determine which ink price to use based on whether it's a color print
    if data.is_color:
        effective_ink_price_per_kg = machine_config.ink_price_cmy
    else:
        effective_ink_price_per_kg = machine_config.ink_price_k
        
    # Calculate ink cost per copy (ink price is in €/kg, so convert grams to kg)
    ink_cost_per_copy = (ink_grams_per_copy / 1000) * effective_ink_price_per_kg
    
    # Calculate total ink cost for all copies
    total_ink_cost = ink_cost_per_copy * data.copies
    
    # Calculate ink cost per page (ALWAYS BASED ON SIMPLEX AREA)
    single_page_area_m2 = (data.page_width / 1000) * (data.page_height / 1000)
    ink_grams_per_simplex_page = single_page_area_m2 * (coverage_percentage * 100) * machine_config.ink_consumption
    simplex_ink_cost_per_page = (ink_grams_per_simplex_page / 1000) * effective_ink_price_per_kg
    
    return ink_cost_per_copy, total_ink_cost, simplex_ink_cost_per_page, ink_grams_per_copy, total_ink_grams

@router.post("/calculate", response_model=CalculationOutput)
def calculate_production(
    data: CalculationInput,
    paper_roll_service: PaperRollService = Depends(get_paper_roll_service),
    machine_config_service: MachineConfigService = Depends(get_machine_config_service)
):
    # Validate page width against machine/roll width first
    # This now passes paper_roll_service to use the correct width
    validate_page_width(data, paper_roll_service)

    # Calculate products per width based on actual roll or machine width
    # This value is also re-calculated/confirmed within calculate_linear_meters for orientation optimization
    products_per_width = calculate_products_per_width(data, paper_roll_service) 

    # Calculate linear meters per copy
    # This now passes paper_roll_service
    linear_meters_per_copy = calculate_linear_meters(data, paper_roll_service)
    
    # New calculation for optimized total_linear_meters
    optimized_total_linear_meters: float = 0
    if products_per_width > 0 and data.copies > 0:
        num_advances = math.ceil(data.copies / products_per_width)
        optimized_total_linear_meters = num_advances * linear_meters_per_copy
    elif data.copies == 0:
        optimized_total_linear_meters = 0
    else: # Should ideally not happen if products_per_width is validated to be > 0 when copies > 0
        optimized_total_linear_meters = linear_meters_per_copy * data.copies

    # Calculate speed and print time
    speed_meters_per_minute = calculate_speed(data)
    print_time_minutes_per_copy = linear_meters_per_copy / speed_meters_per_minute if speed_meters_per_minute > 0 else 0
    # Use optimized total linear meters for total print time
    total_print_time_minutes = optimized_total_linear_meters / speed_meters_per_minute if speed_meters_per_minute > 0 else 0

    # Paper cost calculation
    """ paper_cost = None
    selected_paper_roll_width_cm = None # For output
    paper_roll_supplier = None
    paper_roll_quality = None
    paper_roll_grammage = None """

    if data.paper_roll_id:
        paper_roll = paper_roll_service.get_by_id(data.paper_roll_id)
        if paper_roll: # Should be found if validate_page_width and others didn't raise
            selected_paper_roll_width_cm = paper_roll.width # Store in cm for output
            paper_roll_supplier = paper_roll.supplier
            paper_roll_quality = paper_roll.quality
            paper_roll_grammage = paper_roll.grammage
            if paper_roll.price_per_ton and paper_roll.weight and paper_roll.linear_meters and paper_roll.linear_meters > 0:
                cost_per_kg = paper_roll.price_per_ton / 1000
                
                # Optimized total paper cost calculation
                actual_roll_width_for_costing_mm = _get_effective_roll_width_mm(data, paper_roll_service)
                actual_roll_width_m = actual_roll_width_for_costing_mm / 1000
                total_area_m2_consumed = actual_roll_width_m * optimized_total_linear_meters # Use optimized meters
                total_weight_kg_consumed = total_area_m2_consumed * (paper_roll.grammage / 1000) # grammage is g/m2
                paper_cost = total_weight_kg_consumed * cost_per_kg
        else:
            # This case should ideally be caught earlier by _get_effective_roll_width_mm
            # if an invalid paper_roll_id was provided.
            pass 

    # Ink cost calculation
    ink_cost_per_copy, total_ink_cost, ink_cost_per_page, ink_grams_per_copy, total_ink_grams = calculate_ink_usage(
        data, 
        linear_meters_per_copy, 
        machine_config_service
    )
    
    # Calculate total pages
    total_pages_printed = data.pages * data.copies
    
    # Create the response
    response = CalculationOutput(
        linear_meters_per_copy=round(linear_meters_per_copy, 2),
        total_linear_meters=round(optimized_total_linear_meters, 2), # Use optimized
        products_per_width=products_per_width,
        speed_meters_per_minute=round(speed_meters_per_minute, 2),
        print_time_minutes_per_copy=round(print_time_minutes_per_copy, 2),
        total_print_time_minutes=round(total_print_time_minutes, 2), # Uses optimized
        copies=data.copies,
        total_pages=total_pages_printed,
        paper_roll_id=data.paper_roll_id,
        paper_roll_supplier=paper_roll_supplier,
        paper_roll_quality=paper_roll_quality,
        paper_roll_width=selected_paper_roll_width_cm, # Output in cm
        paper_roll_grammage=paper_roll_grammage,
        paper_cost=paper_cost,
        ink_cost_per_copy=round(ink_cost_per_copy, 2),
        total_ink_cost=round(total_ink_cost, 2),
        ink_cost_per_page=round(ink_cost_per_page, 4),
        ink_grams_per_copy=round(ink_grams_per_copy, 2),
        total_ink_grams=round(total_ink_grams, 2)
    )
    return response

@router.get("/paper-rolls", response_model=list)
def get_paper_rolls(paper_roll_service: PaperRollService = Depends(get_paper_roll_service)):
    """Get all available paper rolls for the calculator."""
    return paper_roll_service.get_all()
