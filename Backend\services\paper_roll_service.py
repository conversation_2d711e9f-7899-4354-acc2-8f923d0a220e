import json
import os
import uuid
from datetime import datetime
from typing import List, Optional
from models.paper_roll import PaperRoll, PaperRollCreate, PaperRollUpdate

class PaperRollService:
    def __init__(self, data_file: str = "data/paper_rolls.json"):
        self.data_file = data_file
        self.ensure_data_file_exists()
    
    def ensure_data_file_exists(self):
        """Ensure the data file exists, create it if it doesn't."""
        if not os.path.exists(self.data_file):
            with open(self.data_file, "w") as f:
                json.dump([], f)
    
    def _load_data(self) -> List[dict]:
        """Load all paper rolls from the data file."""
        with open(self.data_file, "r") as f:
            return json.load(f)
    
    def _save_data(self, data: List[dict]):
        """Save all paper rolls to the data file."""
        with open(self.data_file, "w") as f:
            json.dump(data, f, default=str)
    
    def get_all(self) -> List[PaperRoll]:
        """Get all paper rolls."""
        data = self._load_data()
        return [PaperRoll(**item) for item in data]
    
    def get_by_id(self, roll_id: str) -> Optional[PaperRoll]:
        """Get a paper roll by its ID."""
        data = self._load_data()
        for item in data:
            if item["id"] == roll_id:
                return PaperRoll(**item)
        return None
    
    def calculate_linear_meters(self, width: float, grammage: int, weight: float) -> float:
        """Calculate linear meters from paper roll properties.
        
        Formula: linear_meters = (weight * 1000) / (width * grammage)
        where:
        - weight is in kg
        - width is in cm
        - grammage is in g/m²
        - result is in meters
        """
        # Convert width from cm to m
        width_m = width / 100
        
        # Calculate linear meters
        # (weight in kg * 1000 to get grams) / (width in m * grammage in g/m²)
        linear_meters = (weight * 1000) / (width_m * grammage)
        
        return round(linear_meters, 2)
        
    def calculate_price_per_m2(self, price_per_ton: float, grammage: int) -> float:
        """Calculate price per square meter from price per ton and grammage.
        
        Formula: price_per_m2 = (price_per_ton / 1000) * (grammage / 1000)
        where:
        - price_per_ton is in €/ton
        - grammage is in g/m²
        - result is in €/m²
        """
        if price_per_ton is None:
            return None
            
        # Calculate price per square meter
        # (price_per_ton in €/ton / 1000 to get €/kg) * (grammage in g/m² / 1000 to get kg/m²)
        price_per_m2 = (price_per_ton / 1000) * (grammage / 1000)
        
        return round(price_per_m2, 4)
    
    def create(self, paper_roll: PaperRollCreate) -> PaperRoll:
        """Create a new paper roll."""
        data = self._load_data()
        
        # Calculate linear meters
        linear_meters = self.calculate_linear_meters(
            width=paper_roll.width,
            grammage=paper_roll.grammage,
            weight=paper_roll.weight
        )
        
        # Calculate price per square meter
        price_per_m2 = self.calculate_price_per_m2(
            price_per_ton=paper_roll.price_per_ton,
            grammage=paper_roll.grammage
        )
        
        # Create new paper roll with ID, timestamps, linear meters, and price per m2
        now = datetime.now()
        new_roll = {
            **paper_roll.model_dump(),
            "id": str(uuid.uuid4()),
            "created_at": now,
            "updated_at": None,
            "linear_meters": linear_meters,
            "price_per_m2": price_per_m2
        }
        
        data.append(new_roll)
        self._save_data(data)
        
        return PaperRoll(**new_roll)
    
    def update(self, roll_id: str, paper_roll: PaperRollUpdate) -> Optional[PaperRoll]:
        """Update an existing paper roll."""
        data = self._load_data()
        
        for i, item in enumerate(data):
            if item["id"] == roll_id:
                # Update only the fields that are provided
                update_data = paper_roll.model_dump(exclude_unset=True)
                
                # Create a temporary updated item to check if we need to recalculate linear meters
                temp_updated_item = {**item, **update_data}
                
                # Check if any of the parameters affecting linear meters calculation has changed
                recalculate_linear_meters = any(field in update_data for field in ['width', 'grammage', 'weight'])
                
                if recalculate_linear_meters:
                    # Recalculate linear meters
                    linear_meters = self.calculate_linear_meters(
                        width=temp_updated_item["width"],
                        grammage=temp_updated_item["grammage"],
                        weight=temp_updated_item["weight"]
                    )
                    update_data["linear_meters"] = linear_meters
                
                # Check if any of the parameters affecting price per m2 calculation has changed
                recalculate_price_per_m2 = any(field in update_data for field in ['price_per_ton', 'grammage'])
                
                if recalculate_price_per_m2:
                    # Recalculate price per square meter
                    price_per_m2 = self.calculate_price_per_m2(
                        price_per_ton=temp_updated_item.get("price_per_ton"),
                        grammage=temp_updated_item["grammage"]
                    )
                    update_data["price_per_m2"] = price_per_m2
                
                # Update the paper roll
                data[i] = {
                    **item,
                    **update_data,
                    "updated_at": datetime.now()
                }
                
                self._save_data(data)
                return PaperRoll(**data[i])
        
        return None
    
    def delete(self, roll_id: str) -> bool:
        """Delete a paper roll by its ID."""
        data = self._load_data()
        
        for i, item in enumerate(data):
            if item["id"] == roll_id:
                data.pop(i)
                self._save_data(data)
                return True
        
        return False
    
    # Removed seed_initial_data method as we're now using the data file directly
