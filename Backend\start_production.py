#!/usr/bin/env python3
"""
Servidor de producción para Atexco Calculator
"""

import os
import uvicorn
from main import app

def start_production_server():
    """Iniciar servidor en modo producción"""
    
    print("🚀 Iniciando Atexco Calculator - Modo Producción")
    print("=" * 50)
    
    # Configuración de producción
    host = os.getenv("HOST", "0.0.0.0")
    port = int(os.getenv("PORT", 3006))
    workers = int(os.getenv("WORKERS", 1))
    
    print(f"🌐 Host: {host}")
    print(f"🔌 Puerto: {port}")
    print(f"👥 Workers: {workers}")
    print(f"📧 OAuth: {'✅ Configurado' if check_oauth_config() else '❌ No configurado'}")
    print("-" * 50)
    
    # Configuración de uvicorn para producción
    config = {
        "app": "main:app",
        "host": host,
        "port": port,
        "workers": workers,
        "log_level": "info",
        "access_log": True,
        "reload": False,  # No reload en producción
        "loop": "auto",
        "http": "auto"
    }
    
    # Si hay múltiples workers, usar Gunicorn
    if workers > 1:
        print("🔄 Usando múltiples workers con Gunicorn...")
        try:
            import gunicorn.app.wsgiapp as wsgi
            # Configurar Gunicorn aquí si es necesario
        except ImportError:
            print("⚠️  Gunicorn no disponible, usando un solo worker")
            config["workers"] = 1
    
    print("🚀 Iniciando servidor...")
    
    try:
        uvicorn.run(**config)
    except KeyboardInterrupt:
        print("\n🛑 Servidor detenido por el usuario")
    except Exception as e:
        print(f"❌ Error iniciando servidor: {e}")
        import traceback
        traceback.print_exc()

def check_oauth_config():
    """Verificar configuración OAuth"""
    try:
        import json
        
        # Verificar archivo de configuración
        if not os.path.exists("config/email_config.json"):
            return False
        
        with open("config/email_config.json", 'r') as f:
            config = json.load(f)
        
        oauth_config = config.get("oauth2", {})
        client_secret = oauth_config.get("client_secret", "")
        
        if client_secret == "REPLACE_WITH_YOUR_CLIENT_SECRET":
            return False
        
        # Verificar tokens
        if not os.path.exists("data/oauth_tokens.json"):
            return False
        
        with open("data/oauth_tokens.json", 'r') as f:
            tokens = json.load(f)
        
        return bool(tokens and tokens.get("access_token"))
        
    except Exception:
        return False

if __name__ == "__main__":
    start_production_server()
