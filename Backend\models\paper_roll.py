from pydantic import BaseModel, Field, field_validator
from typing import Optional
from datetime import datetime
import uuid

class PaperRollBase(BaseModel):
    supplier: str = Field(..., description="Proveedor de la bobina")
    quality: str = Field(..., description="Calidad del papel")
    width: float = Field(..., description="Ancho de la bobina en cm")
    grammage: int = Field(..., description="Gramaje del papel en g/m²")
    weight: float = Field(..., description="Peso de la bobina en kg")
    price_per_ton: Optional[float] = Field(None, description="Precio por tonelada en €")
    
    @field_validator('width')
    def validate_width(cls, v):
        if v <= 0:
            raise ValueError("El ancho debe ser mayor que cero")
        return v
    
    @field_validator('grammage')
    def validate_grammage(cls, v):
        if v <= 0:
            raise ValueError("El gramaje debe ser mayor que cero")
        return v
    
    @field_validator('weight')
    def validate_weight(cls, v):
        if v <= 0:
            raise ValueError("El peso debe ser mayor que cero")
        return v
    
    @field_validator('price_per_ton')
    def validate_price(cls, v):
        if v is not None and v <= 0:
            raise ValueError("El precio debe ser mayor que cero")
        return v

class PaperRollCreate(PaperRollBase):
    pass

class PaperRollUpdate(BaseModel):
    supplier: Optional[str] = None
    quality: Optional[str] = None
    width: Optional[float] = None
    grammage: Optional[int] = None
    weight: Optional[float] = None
    price_per_ton: Optional[float] = None
    
    @field_validator('width')
    def validate_width(cls, v):
        if v is not None and v <= 0:
            raise ValueError("El ancho debe ser mayor que cero")
        return v
    
    @field_validator('grammage')
    def validate_grammage(cls, v):
        if v is not None and v <= 0:
            raise ValueError("El gramaje debe ser mayor que cero")
        return v
    
    @field_validator('weight')
    def validate_weight(cls, v):
        if v is not None and v <= 0:
            raise ValueError("El peso debe ser mayor que cero")
        return v
    
    @field_validator('price_per_ton')
    def validate_price(cls, v):
        if v is not None and v <= 0:
            raise ValueError("El precio debe ser mayor que cero")
        return v

class PaperRoll(PaperRollBase):
    id: str
    created_at: datetime
    updated_at: Optional[datetime] = None
    linear_meters: Optional[float] = None
    price_per_m2: Optional[float] = None
    
    class Config:
        from_attributes = True
