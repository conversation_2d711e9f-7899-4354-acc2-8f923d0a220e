import { useState, useEffect } from 'react';
import {
  Box,
  Button,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
  IconButton,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TextField,
  Typography,
  Snackbar,
  Alert
} from '@mui/material';
import EditIcon from '@mui/icons-material/Edit';
import DeleteIcon from '@mui/icons-material/Delete';
import AddIcon from '@mui/icons-material/Add';
import { PaperRoll, PaperRollCreate, PaperRollUpdate } from '../models/PaperRoll';
import { paperRollService } from '../services/paperRollService';

export const PaperRollCatalog = () => {
  const [paperRolls, setPaperRolls] = useState<PaperRoll[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [openDialog, setOpenDialog] = useState(false);
  const [openDeleteDialog, setOpenDeleteDialog] = useState(false);
  const [currentRoll, setCurrentRoll] = useState<PaperRoll | null>(null);
  const [formData, setFormData] = useState<PaperRollCreate | PaperRollUpdate>({
    supplier: '',
    quality: '',
    width: 0,
    grammage: 0,
    weight: 0,
    price_per_ton: 0
  });
  const [snackbar, setSnackbar] = useState({
    open: false,
    message: '',
    severity: 'success' as 'success' | 'error' | 'info' | 'warning'
  });

  useEffect(() => {
    fetchPaperRolls();
  }, []);

  const fetchPaperRolls = async () => {
    try {
      setLoading(true);
      const data = await paperRollService.getAll();
      setPaperRolls(data);
      setError(null);
    } catch (err) {
      console.error('Error fetching paper rolls:', err);
      setError('Error al cargar el catálogo de bobinas');
    } finally {
      setLoading(false);
    }
  };

  const handleOpenDialog = (roll?: PaperRoll) => {
    if (roll) {
      setCurrentRoll(roll);
      setFormData({
        supplier: roll.supplier,
        quality: roll.quality,
        width: roll.width,
        grammage: roll.grammage,
        weight: roll.weight,
        price_per_ton: roll.price_per_ton
      });
    } else {
      setCurrentRoll(null);
      setFormData({
        supplier: '',
        quality: '',
        width: 0,
        grammage: 0,
        weight: 0,
        price_per_ton: 0
      });
    }
    setOpenDialog(true);
  };

  const handleCloseDialog = () => {
    setOpenDialog(false);
  };

  const handleOpenDeleteDialog = (roll: PaperRoll) => {
    setCurrentRoll(roll);
    setOpenDeleteDialog(true);
  };

  const handleCloseDeleteDialog = () => {
    setOpenDeleteDialog(false);
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: name === 'supplier' || name === 'quality' ? value : Number(value)
    });
  };

  const handleSubmit = async () => {
    try {
      if (currentRoll) {
        // Update existing roll
        await paperRollService.update(currentRoll.id, formData);
        setSnackbar({
          open: true,
          message: 'Bobina actualizada correctamente',
          severity: 'success'
        });
      } else {
        // Create new roll
        await paperRollService.create(formData as PaperRollCreate);
        setSnackbar({
          open: true,
          message: 'Bobina creada correctamente',
          severity: 'success'
        });
      }
      handleCloseDialog();
      fetchPaperRolls();
    } catch (err) {
      console.error('Error saving paper roll:', err);
      setSnackbar({
        open: true,
        message: 'Error al guardar la bobina',
        severity: 'error'
      });
    }
  };

  const handleDelete = async () => {
    if (!currentRoll) return;

    try {
      await paperRollService.delete(currentRoll.id);
      setSnackbar({
        open: true,
        message: 'Bobina eliminada correctamente',
        severity: 'success'
      });
      handleCloseDeleteDialog();
      fetchPaperRolls();
    } catch (err) {
      console.error('Error deleting paper roll:', err);
      setSnackbar({
        open: true,
        message: 'Error al eliminar la bobina',
        severity: 'error'
      });
    }
  };

  // Removed seed data function as we're now using the data file directly

  const handleCloseSnackbar = () => {
    setSnackbar({
      ...snackbar,
      open: false
    });
  };

  return (
    <Box sx={{ width: '100%' }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 2 }}>
        <Typography variant="h5" component="h2">
          Catálogo de Bobinas
        </Typography>
        <Box>
          <Button
            variant="contained"
            color="primary"
            startIcon={<AddIcon />}
            onClick={() => handleOpenDialog()}
            sx={{ mr: 1 }}
          >
            Nueva Bobina
          </Button>
          {/* Removed 'Load Initial Data' button */}
        </Box>
      </Box>

      {loading ? (
        <Typography>Cargando...</Typography>
      ) : error ? (
        <Typography color="error">{error}</Typography>
      ) : (
        <TableContainer component={Paper}>
          <Table sx={{ minWidth: 650 }} aria-label="paper rolls table">
            <TableHead>
              <TableRow>
                <TableCell>Proveedor</TableCell>
                <TableCell>Calidad</TableCell>
                <TableCell align="right">Ancho (cm)</TableCell>
                <TableCell align="right">Gramaje (g/m²)</TableCell>
                <TableCell align="right">Peso (kg)</TableCell>
                <TableCell align="right">Metros Lineales</TableCell>
                <TableCell align="right">Precio (€/ton)</TableCell>
                <TableCell align="right">Precio (€/m²)</TableCell>
                <TableCell align="center">Acciones</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {paperRolls.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={9} align="center">
                    No hay bobinas en el catálogo
                  </TableCell>
                </TableRow>
              ) : (
                paperRolls.map((roll) => (
                  <TableRow key={roll.id}>
                    <TableCell>{roll.supplier}</TableCell>
                    <TableCell>{roll.quality}</TableCell>
                    <TableCell align="right">{roll.width}</TableCell>
                    <TableCell align="right">{roll.grammage}</TableCell>
                    <TableCell align="right">{roll.weight}</TableCell>
                    <TableCell align="right">
                      {roll.linear_meters ? `${roll.linear_meters.toLocaleString()} m` : 'N/A'}
                    </TableCell>
                    <TableCell align="right">
                      {roll.price_per_ton ? `${roll.price_per_ton} €/ton` : 'N/A'}
                    </TableCell>
                    <TableCell align="right">
                      {roll.price_per_m2 ? `${roll.price_per_m2.toFixed(4)} €/m²` : 'N/A'}
                    </TableCell>
                    <TableCell align="center">
                      <IconButton
                        color="primary"
                        onClick={() => handleOpenDialog(roll)}
                        size="small"
                      >
                        <EditIcon />
                      </IconButton>
                      <IconButton
                        color="error"
                        onClick={() => handleOpenDeleteDialog(roll)}
                        size="small"
                      >
                        <DeleteIcon />
                      </IconButton>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </TableContainer>
      )}

      {/* Form Dialog */}
      <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth="sm" fullWidth>
        <DialogTitle>{currentRoll ? 'Editar Bobina' : 'Nueva Bobina'}</DialogTitle>
        <DialogContent>
          <Box component="form" sx={{ mt: 1 }}>
            <TextField
              margin="normal"
              required
              fullWidth
              label="Proveedor"
              name="supplier"
              value={formData.supplier}
              onChange={handleInputChange}
            />
            <TextField
              margin="normal"
              required
              fullWidth
              label="Calidad"
              name="quality"
              value={formData.quality}
              onChange={handleInputChange}
            />
            <TextField
              margin="normal"
              required
              fullWidth
              label="Ancho (cm)"
              name="width"
              type="number"
              value={formData.width}
              onChange={handleInputChange}
              inputProps={{ min: 0, step: "0.1" }}
            />
            <TextField
              margin="normal"
              required
              fullWidth
              label="Gramaje (g/m²)"
              name="grammage"
              type="number"
              value={formData.grammage}
              onChange={handleInputChange}
              inputProps={{ min: 0 }}
            />
            <TextField
              margin="normal"
              required
              fullWidth
              label="Peso (kg)"
              name="weight"
              type="number"
              value={formData.weight}
              onChange={handleInputChange}
              inputProps={{ min: 0, step: "0.1" }}
            />
            <TextField
              margin="normal"
              fullWidth
              label="Precio (€/ton)"
              name="price_per_ton"
              type="number"
              value={formData.price_per_ton}
              onChange={handleInputChange}
              inputProps={{ min: 0, step: "0.01" }}
            />
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDialog}>Cancelar</Button>
          <Button onClick={handleSubmit} variant="contained">
            Guardar
          </Button>
        </DialogActions>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <Dialog
        open={openDeleteDialog}
        onClose={handleCloseDeleteDialog}
      >
        <DialogTitle>Confirmar eliminación</DialogTitle>
        <DialogContent>
          <DialogContentText>
            ¿Estás seguro de que deseas eliminar esta bobina?
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDeleteDialog}>Cancelar</Button>
          <Button onClick={handleDelete} color="error" variant="contained">
            Eliminar
          </Button>
        </DialogActions>
      </Dialog>

      {/* Snackbar for notifications */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={handleCloseSnackbar}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
      >
        <Alert onClose={handleCloseSnackbar} severity={snackbar.severity}>
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Box>
  );
};
