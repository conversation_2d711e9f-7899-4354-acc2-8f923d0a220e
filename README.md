# Atexco Vega Calculator

A client-server application for calculating production metrics for Atexco Vega digital roll-to-roll printing machines.

## Project Structure

- `Backend/`: FastAPI backend API
- `Frontend/`: React + Vite frontend application
- `docker-compose.yml`: Docker Compose configuration for containerized deployment

## Backend Setup

1. Navigate to the backend directory:
   ```
   cd backend
   ```

2. Create a virtual environment (optional but recommended):
   ```
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   ```

3. Install dependencies:
   ```
   pip install -r requirements.txt
   ```

4. Run the server:
   ```
   uvicorn main:app --reload
   ```

The API will be available at http://localhost:3006. You can access the API documentation at http://localhost:3006/docs.

## Frontend Setup

1. Navigate to the frontend directory:
   ```
   cd frontend
   ```

2. Install dependencies:
   ```
   npm install
   ```

3. Run the development server:
   ```
   npm run dev
   ```

The frontend will be available at http://localhost:4006.

## Docker Compose Setup

You can run the entire application using Docker Compose:

1. Make sure you have Docker and Docker Compose installed on your system.

2. From the root directory of the project, build and start the containers:
   ```
   docker-compose up -d --build
   ```

3. The application will be available at:
   - Frontend: http://localhost:4006
   - Backend API: http://localhost:3006
   - API Documentation: http://localhost:3006/docs

4. To stop the containers:
   ```
   docker-compose down
   ```

## Features

- **Email Authentication**: Secure access with 6-digit verification codes sent via email
- Calculate production metrics for Vega 440, 660, and 880 models
- Support for different page sizes and custom dimensions
- Duplex/simplex printing options
- Color (CMYK) or black (K) only printing
- Coverage percentage adjustment
- Visual representation of results with charts
- User session management with JWT tokens

## Authentication Setup

Before using the application, you need to configure the email authentication system:

1. **Configure OAuth 2.0**: Follow the instructions in `Backend/AUTH_SETUP.md`
2. **Set up email credentials**: Edit `Backend/config/email_config.json` with your Google OAuth credentials
3. **Run setup script**: Execute `python Backend/setup_auth.py` to complete the OAuth configuration

## API Endpoints

### Authentication
- `POST /auth/send-code`: Send verification code to email
- `POST /auth/verify-code`: Verify code and get JWT token
- `GET /auth/verify-token`: Verify if current token is valid
- `GET /auth/oauth/setup-url`: Get OAuth authorization URL (setup only)
- `GET /auth/oauth/callback`: Handle OAuth callback (setup only)

### Application
- `GET /`: Welcome message
- `POST /calculate`: Calculate production metrics based on input parameters (requires authentication)
- `GET /paper-rolls`: Get paper roll catalog (requires authentication)
- `GET /machine-config`: Get machine configurations (requires authentication)

## Calculation Logic

The calculator takes into account:
- Machine model (440, 660, 880) which determines the maximum width
- Page dimensions with 5mm margins on each side
- Number of pages
- Duplex/simplex printing
- Coverage percentage (affects printing speed)
- Color mode (CMYK or K only)

The calculator determines:
- How many products can fit across the width of the machine
- Total linear meters required for the job
- Printing speed based on coverage and color mode
- Total print time
