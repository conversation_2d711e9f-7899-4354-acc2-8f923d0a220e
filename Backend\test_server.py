#!/usr/bin/env python3
"""
Simple test script to verify the server can start
"""

def test_imports():
    """Test that all imports work"""
    try:
        print("Testing imports...")
        
        # Test basic FastAPI
        from fastapi import FastAPI
        print("✅ FastAPI import OK")
        
        # Test routes
        from routes.base import router as base_router
        print("✅ Base router OK")
        
        from routes.test_auth import router as test_auth_router
        print("✅ Test auth router OK")
        
        # Test main app
        from main import app
        print("✅ Main app OK")
        
        return True
        
    except Exception as e:
        print(f"❌ Import error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_auth_endpoints():
    """Test auth endpoints directly"""
    try:
        print("\nTesting auth endpoints...")
        
        # Import the test auth functions
        from routes.test_auth import verification_codes
        import random
        from datetime import datetime, timedelta
        
        # Test code generation
        code = str(random.randint(100000, 999999))
        email = "<EMAIL>"
        
        # Simulate storing a code
        verification_codes[email] = {
            'code': code,
            'expires_at': datetime.now() + timedelta(minutes=10),
            'attempts': 0
        }
        
        print(f"✅ Generated code {code} for {email}")
        
        # Test verification logic
        if email in verification_codes:
            stored_data = verification_codes[email]
            if stored_data['code'] == code:
                print("✅ Code verification logic OK")
                return True
        
        return False
        
    except Exception as e:
        print(f"❌ Auth test error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("=== Server Test ===\n")
    
    # Test imports
    if not test_imports():
        print("\n❌ Import tests failed")
        exit(1)
    
    # Test auth
    if not test_auth_endpoints():
        print("\n❌ Auth tests failed")
        exit(1)
    
    print("\n✅ All tests passed!")
    print("The server should be able to start successfully.")
    print("\nTo start the server, run: python main.py")
