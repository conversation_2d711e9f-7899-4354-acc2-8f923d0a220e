#!/usr/bin/env python3
"""
Development server starter with better error handling
"""

def start_server():
    try:
        print("🚀 Starting Atexco Calculator Development Server...")
        print("📍 Server will be available at: http://localhost:3006")
        print("📖 API Documentation: http://localhost:3006/docs")
        print("🔧 Test Auth Status: http://localhost:3006/test-auth/status")
        print("-" * 50)
        
        # Import and start
        import uvicorn
        from main import app
        
        # Start server
        uvicorn.run(
            app, 
            host="0.0.0.0", 
            port=3006, 
            reload=True,
            log_level="info"
        )
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        print("💡 Make sure you're in the virtual environment and dependencies are installed")
        print("   Run: pip install -r requirements.txt")
        
    except Exception as e:
        print(f"❌ Server error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    start_server()
