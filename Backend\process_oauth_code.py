#!/usr/bin/env python3
"""
Procesar código de autorización OAuth manualmente
"""

from services.auth_service import AuthService

def process_oauth_code():
    print("=== Procesar Código de Autorización OAuth ===\n")
    
    # El código que obtuviste
    authorization_code = "4/0AUJR-x7Mjtase8xBtYe4QHTW3yNRCPcektFUHWm-l-MDEqj_fbE7sfVms0nbRSV3WgQ1yA"
    
    try:
        # Crear servicio de auth
        auth_service = AuthService()
        
        print(f"🔄 Procesando código: {authorization_code[:30]}...")
        
        # Procesar el código
        success = auth_service.handle_oauth_callback(authorization_code)
        
        if success:
            print("✅ ¡OAuth configurado exitosamente!")
            print("🎉 Gmail OAuth 2.0 está <NAME_EMAIL>")
            
            # Probar envío de email
            print("\n" + "="*50)
            test_email = input("¿Quieres probar enviando un email real? Ingresa un email de prueba: ").strip()
            
            if test_email:
                print(f"\n📧 Enviando código de verificación a {test_email}...")
                print("   Usando Gmail OAuth 2.0 con <EMAIL>")
                
                try:
                    result = auth_service.send_verification_code(test_email)
                    if result:
                        print("✅ ¡Email enviado exitosamente!")
                        print("🔍 Revisa la bandeja de entrada (y carpeta de spam)")
                        print("📧 El email fue <NAME_EMAIL>")
                        
                        # Mostrar el código generado para pruebas
                        import json
                        with open('data/verification_codes.json', 'r') as f:
                            codes = json.load(f)
                        
                        if codes:
                            latest_code = codes[-1]
                            if latest_code['email'] == test_email:
                                print(f"\n🔢 Código generado: {latest_code['code']}")
                                print("   (Para pruebas - normalmente solo estaría en el email)")
                    else:
                        print("❌ Error al enviar email")
                except Exception as e:
                    print(f"❌ Error al enviar email: {e}")
                    import traceback
                    traceback.print_exc()
            
            return True
        else:
            print("❌ Error al procesar el código OAuth")
            return False
            
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = process_oauth_code()
    
    if success:
        print("\n🎉 ¡Configuración OAuth completada!")
        print("El sistema puede ahora enviar emails reales usando Gmail.")
    else:
        print("\n❌ La configuración no se completó.")
    
    input("\nPresiona Enter para continuar...")
