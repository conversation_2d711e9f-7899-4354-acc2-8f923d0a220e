import React, { useState, useEffect } from 'react';
import { 
  Box, 
  Card, 
  CardContent, 
  TextField, 
  FormControl, 
  InputLabel, 
  Select, 
  MenuItem, 
  FormControlLabel, 
  Switch, 
  Button, 
  Typography, 
  Divider,
  Paper,
  Alert,
  Slider,
  CircularProgress,
  Grid,
  Tooltip
} from '@mui/material';
import ScreenRotationIcon from '@mui/icons-material/ScreenRotation';
import { SelectChangeEvent } from '@mui/material/Select';
import { getPaperRollsForCalculator, calculateProduction, CalculationInput } from '../services/paperRollCalculatorService';
import { PaperRoll } from '../models/PaperRoll';
import { Chart as ChartJS, ArcElement, Tooltip as ChartTooltip, Legend, CategoryScale, LinearScale, BarElement, Title } from 'chart.js';
import { Pie, Bar } from 'react-chartjs-2';

// Register ChartJS components
ChartJS.register(ArcElement, CategoryScale, LinearScale, BarElement, Title, ChartTooltip, Legend);

// Types
interface FormData {
  machineModel: string;
  pageWidth: number;
  pageHeight: number;
  pages: number;
  isDuplex: boolean;
  coverage: number;
  isColor: boolean;
  copies: number;
  paperRollId: string | null;
}

interface CalculationResult {
  linearMetersPerCopy: number;
  totalLinearMeters: number;
  productsPerWidth: number;
  printTimeMinutesPerCopy: number;
  totalPrintTimeMinutes: number;
  speedMetersPerMinute: number;
  copies: number;
  totalPages: number;
  paperRollId?: string | null;
  paperRollSupplier?: string | null;
  paperRollQuality?: string | null;
  paperRollWidth?: number | null;
  paperRollGrammage?: number | null;
  paperCost?: number | null;
  inkCostPerCopy?: number | null;
  totalInkCost?: number | null;
  inkCostPerPage?: number | null;
  inkGramsPerCopy?: number | null;
  totalInkGrams?: number | null;
}

export const Calculator: React.FC = () => {
  // State
  const [formData, setFormData] = useState<FormData>({
    machineModel: '440',
    pageWidth: 210, // Default A4 width
    pageHeight: 297, // Default A4 height
    pages: 1,
    isDuplex: true, // Duplex activado por defecto
    coverage: 30, // Default 30% coverage
    isColor: true,
    copies: 1, // Default 1 copy
    paperRollId: null, // No paper roll selected by default
  });
  
  const [paperRolls, setPaperRolls] = useState<PaperRoll[]>([]);
  
  const [result, setResult] = useState<CalculationResult | null>(null);
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);

  // Handlers
  const handleTextChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    const numValue = parseFloat(value);
    
    if (!isNaN(numValue)) {
      setFormData({ ...formData, [name]: numValue });
    } else if (value === '') {
      setFormData({ ...formData, [name]: '' });
    }
  };

  const handleSelectChange = (e: SelectChangeEvent<string>) => {
    const { name, value } = e.target;
    if (name === 'machineModel') {
      setFormData({ ...formData, machineModel: value });
    } else if (name === 'paperRollId') {
      setFormData({ ...formData, paperRollId: value === '' ? null : value });
    }
  };

  const handleSwitchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, checked } = e.target;
    
    // Si estamos cambiando isColor, ajustamos la cobertura según corresponda
    if (name === 'isColor') {
      // Si estamos activando el color, ajustamos la cobertura proporcionalmente (hasta 400%)
      // Si estamos desactivando el color, limitamos la cobertura a 100%
      const newCoverage = checked 
        ? Math.min(formData.coverage * 4, 400) // Multiplicamos por 4 pero no excedemos 400
        : Math.min(formData.coverage, 100);  // Aseguramos que no exceda 100 en modo monocromo
      
      setFormData({ 
        ...formData, 
        [name]: checked,
        coverage: newCoverage
      });
    } else {
      setFormData({ ...formData, [name]: checked });
    }
  };

  const handleSliderChange = (_event: Event, newValue: number | number[]) => {
    setFormData({ ...formData, coverage: newValue as number });
  };

  // Load paper rolls on component mount
  useEffect(() => {
    const loadPaperRolls = async () => {
      try {
        const rolls = await getPaperRollsForCalculator();
        setPaperRolls(rolls);
      } catch (err) {
        console.error('Error loading paper rolls:', err);
      }
    };
    
    loadPaperRolls();
  }, []);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError(null);

    try {
      // Convert form data to API format
      const apiData: CalculationInput = {
        machine_model: formData.machineModel,
        page_width: formData.pageWidth,
        page_height: formData.pageHeight,
        pages: formData.pages,
        is_duplex: formData.isDuplex,
        coverage: formData.coverage,
        is_color: formData.isColor,
        copies: formData.copies,
        paper_roll_id: formData.paperRollId
      };

      const apiResult = await calculateProduction(apiData);
      
      // Convert API response to our format
      setResult({
        linearMetersPerCopy: apiResult.linear_meters_per_copy,
        totalLinearMeters: apiResult.total_linear_meters,
        productsPerWidth: apiResult.products_per_width,
        printTimeMinutesPerCopy: apiResult.print_time_minutes_per_copy,
        totalPrintTimeMinutes: apiResult.total_print_time_minutes,
        speedMetersPerMinute: apiResult.speed_meters_per_minute,
        copies: apiResult.copies,
        totalPages: apiResult.total_pages,
        paperRollId: apiResult.paper_roll_id,
        paperRollSupplier: apiResult.paper_roll_supplier,
        paperRollQuality: apiResult.paper_roll_quality,
        paperRollWidth: apiResult.paper_roll_width,
        paperRollGrammage: apiResult.paper_roll_grammage,
        paperCost: apiResult.paper_cost,
        inkCostPerCopy: apiResult.ink_cost_per_copy,
        totalInkCost: apiResult.total_ink_cost,
        inkCostPerPage: apiResult.ink_cost_per_page,
        inkGramsPerCopy: apiResult.ink_grams_per_copy,
        totalInkGrams: apiResult.total_ink_grams
      });
    } catch (err: unknown) {
      console.error('Error calculating:', err);
      const errorMessage = err instanceof Error ? err.message : 'Error calculating results. Please check your inputs.';
      setError(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  // Predefined page sizes
  const pageSizes = [
    { name: 'A4', width: 210, height: 297 },
    { name: 'A3', width: 297, height: 420 },
    { name: 'A5', width: 148, height: 210 },
    { name: 'Letter', width: 216, height: 279 },
    { name: 'Legal', width: 216, height: 356 },
    { name: '17x24 cm', width: 170, height: 240 },
    { name: 'Custom', width: 0, height: 0 },
  ];

  const handlePageSizeChange = (e: SelectChangeEvent) => {
    const selectedSize = pageSizes.find(size => size.name === e.target.value);
    if (selectedSize && selectedSize.name !== 'Custom') {
      setFormData({
        ...formData,
        pageWidth: selectedSize.width,
        pageHeight: selectedSize.height,
      });
    }
  };

  let paperCost = 0, totalInkCost = 0, copies = 0, totalPages = 0;
  let inkCostPerCopy = 0, inkCostPerPage = 0, paperCostPerCopy = 0, paperCostPerPage = 0;
  let totalCost = 0, totalCostPerCopy = 0, totalCostPerPage = 0;
  let chartData: any = { pieData: {}, barData: {} }; // Initialize chart data

  if (result) {
    paperCost = Number(result.paperCost) || 0;
    totalInkCost = Number(result.totalInkCost) || 0;
    copies = Number(result.copies) || 0;
    totalPages = Number(result.totalPages) || 0;
    inkCostPerCopy = Number(result.inkCostPerCopy) || 0;
    inkCostPerPage = Number(result.inkCostPerPage) || 0;

    paperCostPerCopy = copies > 0 ? paperCost / copies : 0;
    paperCostPerPage = totalPages > 0 ? paperCost / totalPages : 0;

    totalCost = paperCost + totalInkCost;
    totalCostPerCopy = copies > 0 ? totalCost / copies : 0;
    totalCostPerPage = totalPages > 0 ? totalCost / totalPages : 0;

    chartData = {
      pieData: {
        labels: ['Paper Cost', 'Ink Cost'],
        datasets: [{
          data: [paperCost, totalInkCost],
          backgroundColor: ['rgba(76, 175, 80, 0.6)', 'rgba(255, 152, 0, 0.6)'],
          borderColor: ['rgba(76, 175, 80, 1)', 'rgba(255, 152, 0, 1)'],
          borderWidth: 1,
        }],
      },
      barData: {
        labels: ['Paper', 'Ink'],
        datasets: [
          {
            label: 'Cost per Copy (€)',
            data: [paperCostPerCopy, inkCostPerCopy],
            backgroundColor: ['rgba(76, 175, 80, 0.6)', 'rgba(255, 152, 0, 0.6)'],
            borderColor: ['rgba(76, 175, 80, 1)', 'rgba(255, 152, 0, 1)'],
            borderWidth: 1,
          },
          {
            label: 'Cost per Page (€)',
            data: [paperCostPerPage, inkCostPerPage],
            backgroundColor: ['rgba(46, 125, 50, 0.6)', 'rgba(230, 81, 0, 0.6)'],
            borderColor: ['rgba(46, 125, 50, 1)', 'rgba(230, 81, 0, 1)'],
            borderWidth: 1,
          },
        ],
      }
    };
  }

  // Render charts if we have results
  const renderCharts = () => {
    if (!result) return null;

    const chartOptions = {
      responsive: true,
      maintainAspectRatio: false,
      plugins: {
        legend: {
          position: 'top' as const,
        },
        tooltip: {
          callbacks: {
            label: function (context: any) {
              let label = context.dataset.label || '';
              if (label) {
                label += ': ';
              }
              if (context.parsed !== null) {
                label += new Intl.NumberFormat('de-DE', { style: 'currency', currency: 'EUR' }).format(context.parsed);
              }
              return label;
            }
          }
        }
      },
    };

    return (
      <Grid container spacing={3} sx={{ mt: 2 }}>
        <Grid size={{ xs: 12, md: 6 }}>
          <Paper elevation={3} sx={{ p: 2, height: '350px' }}>
            <Typography variant="h6" gutterBottom align="center">Cost Breakdown</Typography>
            <Box sx={{ height: '280px', position: 'relative' }}>
              <Pie data={chartData.pieData} options={chartOptions} />
            </Box>
          </Paper>
        </Grid>
        <Grid size={{ xs: 12, md: 6 }}>
          <Paper elevation={3} sx={{ p: 2, height: '350px' }}>
            <Typography variant="h6" gutterBottom align="center">Cost Comparison</Typography>
            <Box sx={{ height: '280px', position: 'relative' }}>
              <Bar data={chartData.barData} options={chartOptions} />
            </Box>
          </Paper>
        </Grid>
      </Grid>
    );
  };

  return (
    <Box sx={{ mt: 4, mx: 'auto', maxWidth: '1200px', mb: 4, display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
      <Card elevation={3} sx={{ borderRadius: 2, overflow: 'hidden' }}>
        <CardContent sx={{ p: 3 }}>
          <Typography variant="h5" component="h2" gutterBottom sx={{ fontWeight: 'bold', color: '#1976d2', mb: 3, textAlign: 'center' }}>
            Production Calculator
          </Typography>
          
          <form onSubmit={handleSubmit}>
            <Grid container spacing={3}>
              {/* 1. Modelo -> Bobina de Papel */}
              <Grid size={{ xs: 12 }}>
                <Paper elevation={1} sx={{ p: 2, mb: 3, borderRadius: 2, bgcolor: '#f8f9fa' }}>
                  <Typography variant="subtitle1" sx={{ mb: 2, fontWeight: 'bold', color: '#1976d2' }}>
                    1. Machine and Material Selection
                  </Typography>
                  <Grid container spacing={2}>
                    <Grid size={{ xs: 12, md: 4 }}>
                      <FormControl fullWidth variant="outlined">
                        <InputLabel id="machine-model-label">Machine Model</InputLabel>
                        <Select
                          labelId="machine-model-label"
                          name="machineModel"
                          value={formData.machineModel}
                          label="Machine Model"
                          onChange={handleSelectChange}
                        >
                          <MenuItem value="440">Vega 440</MenuItem>
                          <MenuItem value="660">Vega 660</MenuItem>
                          <MenuItem value="880">Vega 880</MenuItem>
                        </Select>
                      </FormControl>
                    </Grid>
                    
                    <Grid size={{ xs: 12, md: 8 }}>
                      <FormControl fullWidth variant="outlined">
                        <InputLabel shrink id="paper-roll-label">Paper Roll</InputLabel>
                        <Select
                          labelId="paper-roll-label"
                          name="paperRollId"
                          value={formData.paperRollId || ''}
                          label="Paper Roll"
                          onChange={handleSelectChange}
                          displayEmpty
                          notched
                          sx={{ 
                            minWidth: '300px',
                            '& .MuiSelect-select': { 
                              minWidth: '300px',
                              whiteSpace: 'normal',
                              letterSpacing: '0.5px',
                              fontFamily: '"Roboto", "Helvetica", "Arial", sans-serif',
                              paddingTop: '16px',
                              paddingBottom: '16px'
                            }
                          }}
                          MenuProps={{
                            PaperProps: {
                              style: {
                                maxHeight: 300,
                                width: 'auto',
                                minWidth: '300px'
                              },
                            },
                          }}
                        >
                          <MenuItem value="" sx={{ minWidth: '300px', letterSpacing: '0.5px', fontFamily: '"Roboto", "Helvetica", "Arial", sans-serif' }}>None (no cost calculation)</MenuItem>
                          {paperRolls.map((roll) => (
                            <MenuItem key={roll.id} value={roll.id} sx={{ minWidth: '300px', letterSpacing: '0.5px', fontFamily: '"Roboto", "Helvetica", "Arial", sans-serif' }}>
                              {roll.supplier} - {roll.quality} ({roll.grammage}g/m²) - {roll.width}cm
                            </MenuItem>
                          ))}
                        </Select>
                      </FormControl>
                    </Grid>
                  </Grid>
                </Paper>
              </Grid>
              
              {/* 2. Número de páginas -> Ejemplares -> Ancho -> Alto */}
              <Grid size={{ xs: 12 }}>
                <Paper elevation={1} sx={{ p: 2, mb: 3, borderRadius: 2, bgcolor: '#f8f9fa' }}>
                  <Typography variant="h5" sx={{ mt: 4, mb: 3, fontWeight: 'bold', color: '#1976d2', textAlign: 'center' }}>
                  Production Results
                </Typography>
                  <Grid container spacing={2}>
                    <Grid size={{ xs: 12, sm: 6, md: 3 }}>
                      <TextField
                        fullWidth
                        variant="outlined"
                        label="Number of Copies"
                        type="number"
                        name="copies"
                        value={formData.copies}
                        onChange={handleTextChange}
                        InputProps={{ inputProps: { min: 1 } }}
                      />
                    </Grid>
                    
                    <Grid size={{ xs: 12, sm: 6, md: 3 }}>
                      <TextField
                        fullWidth
                        variant="outlined"
                        label="Number of Pages"
                        type="number"
                        name="pages"
                        value={formData.pages}
                        onChange={handleTextChange}
                        InputProps={{ inputProps: { min: 1 } }}
                      />
                    </Grid>
                    
                    <Grid size={{ xs: 12, sm: 6, md: 6 }}>
                      <FormControl fullWidth variant="outlined">
                        <InputLabel id="page-size-label">Predefined Page Size</InputLabel>
                        <Select
                          labelId="page-size-label"
                          id="page-size"
                          label="Predefined Page Size"
                          onChange={handlePageSizeChange}
                          defaultValue="A4"
                        >
                          {pageSizes.map((size) => (
                            <MenuItem key={size.name} value={size.name}>
                              {size.name}
                            </MenuItem>
                          ))}
                        </Select>
                      </FormControl>
                    </Grid>
                    
                    <Grid size={{ xs: 12 }}>
                      <Box sx={{ mt: 2, display: 'flex', alignItems: 'center' }}>
                        <Box sx={{ flex: 1, mr: 1 }}>
                          <TextField
                            fullWidth
                            variant="outlined"
                            label="Width (mm)"
                            type="number"
                            name="pageWidth"
                            value={formData.pageWidth}
                            onChange={handleTextChange}
                            InputProps={{ inputProps: { min: 1 } }}
                          />
                        </Box>
                        
                        <Box sx={{ mx: 1, display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
                          <Button 
                            variant="outlined" 
                            color="primary"
                            onClick={() => {
                              // Intercambiar ancho y alto
                              setFormData({
                                ...formData,
                                pageWidth: formData.pageHeight,
                                pageHeight: formData.pageWidth
                              });
                            }}
                            sx={{ 
                              minWidth: '40px', 
                              height: '40px', 
                              p: 0,
                              borderRadius: '50%',
                              '& .MuiButton-startIcon': { margin: 0 }
                            }}
                          >
                            <Tooltip title="Change Orientation (Portrait/Landscape)">
                              <ScreenRotationIcon />
                            </Tooltip>
                          </Button>
                        </Box>
                        
                        <Box sx={{ flex: 1, ml: 1 }}>
                          <TextField
                            fullWidth
                            variant="outlined"
                            label="Height (mm)"
                            type="number"
                            name="pageHeight"
                            value={formData.pageHeight}
                            onChange={handleTextChange}
                            InputProps={{ inputProps: { min: 1 } }}
                          />
                        </Box>
                      </Box>
                    </Grid>
                  </Grid>
                </Paper>
              </Grid>
              
              {/* 3. Coverage -> Duplex -> Color */}
              <Grid size={{ xs: 12 }}>
                <Paper elevation={1} sx={{ p: 2, mb: 3, borderRadius: 2, bgcolor: '#f8f9fa' }}>
                  <Typography variant="subtitle1" sx={{ mb: 2, fontWeight: 'bold', color: '#1976d2' }}>
                    3. Ink Coverage
                  </Typography>
                  <Grid container spacing={2}>
                    <Grid size={{ xs: 12, md: 6 }}>
                      <Typography id="coverage-slider" gutterBottom>
                        Ink Coverage: <strong>{formData.coverage}%</strong>
                      </Typography>
                      <Slider
                        value={formData.coverage}
                        onChange={handleSliderChange}
                        aria-labelledby="coverage-slider"
                        valueLabelDisplay="auto"
                        step={formData.isColor ? 10 : 5}
                        marks
                        min={0}
                        max={formData.isColor ? 400 : 100}
                        sx={{
                          color: '#1976d2',
                          '& .MuiSlider-thumb': {
                            height: 24,
                            width: 24,
                          },
                          '& .MuiSlider-track': {
                            height: 8
                          },
                          '& .MuiSlider-rail': {
                            height: 8
                          }
                        }}
                      />
                    </Grid>
                    
                    <Grid size={{ xs: 12, md: 6 }}>
                      <Box sx={{ display: 'flex', justifyContent: 'space-around', alignItems: 'center', height: '100%' }}>
                        <FormControlLabel
                          control={
                            <Switch
                              checked={formData.isDuplex}
                              onChange={handleSwitchChange}
                              name="isDuplex"
                              color="primary"
                            />
                          }
                          label="Double-sided printing"
                        />
                        
                        <FormControlLabel
                          control={
                            <Switch
                              checked={formData.isColor}
                              onChange={handleSwitchChange}
                              name="isColor"
                              color="primary"
                            />
                          }
                          label="Color printing"
                        />
                      </Box>
                    </Grid>
                  </Grid>
                </Paper>
              </Grid>
              
              {/* 4. Botón de Calculate */}
              <Grid size={{ xs: 12 }}>
                <Button 
                  type="submit" 
                  variant="contained" 
                  color="primary" 
                  fullWidth
                  size="large"
                  disabled={loading}
                  sx={{
                    py: 1.5,
                    fontSize: '1.1rem',
                    fontWeight: 'bold',
                    borderRadius: 2,
                    textTransform: 'none',
                    boxShadow: 3
                  }}
                >
                  {loading ? <CircularProgress size={24} /> : 'Calculate'}
                </Button>
              </Grid>
            </Grid>
          </form>
          
          {/* Error Message */}
          {error && (
            <Alert severity="error" sx={{ mt: 2 }}>
              {error}
            </Alert>
          )}
          
          {/* Results */}
          {result && (
            <Box sx={{ mt: 4 }}>
              <Divider sx={{ mb: 2 }} />
              <Typography variant="h6" gutterBottom>
                Calculation Results
              </Typography>
              
              <Grid container spacing={2}>
                <Grid size={{ xs: 12 }}>
                  <Paper elevation={3} sx={{ p: 3, textAlign: 'center', borderRadius: 2, bgcolor: '#f1f8fe', border: '1px solid #1976d2', mb: 2 }}>
                    <Grid container spacing={2} alignItems="center">
                      <Grid size={{ xs: 12, md: 3 }}>
                        <Box sx={{ textAlign: 'center' }}>
                          <Typography variant="subtitle2" color="text.secondary" sx={{ mb: 1 }}>
                            Copies
                          </Typography>
                          <Typography variant="h3" component="div" color="primary" sx={{ fontWeight: 'bold' }}>
                            {result.copies}
                          </Typography>
                        </Box>
                      </Grid>
                      
                      <Grid size={{ xs: 12, md: 3 }}>
                        <Box sx={{ textAlign: 'center' }}>
                          <Typography variant="subtitle2" color="text.secondary" sx={{ mb: 1 }}>
                            Format (mm)
                          </Typography>
                          <Typography variant="h3" component="div" color="primary" sx={{ fontWeight: 'bold' }}>
                            {formData.pageWidth}x{formData.pageHeight}
                          </Typography>
                        </Box>
                      </Grid>
                      
                      <Grid size={{ xs: 12, md: 3 }}>
                        <Box sx={{ textAlign: 'center' }}>
                          <Typography variant="subtitle2" color="text.secondary" sx={{ mb: 1 }}>
                            Products per Width
                          </Typography>
                          <Typography variant="h3" component="div" color="primary" sx={{ fontWeight: 'bold' }}>
                            {result.productsPerWidth}
                          </Typography>
                        </Box>
                      </Grid>
                      
                      <Grid size={{ xs: 12, md: 3 }}>
                        <Box>
                          <Grid container spacing={1}>
                            <Grid size={{ xs: 6 }}>
                              <Box sx={{ textAlign: 'center', p: 1, border: '1px solid #e0e0e0', borderRadius: 1, m: 0.5 }}>
                                <Typography variant="body2" color="text.secondary">
                                  Coverage
                                </Typography>
                                <Typography variant="body1" color="primary" sx={{ fontWeight: 'medium' }}>
                                  {formData.coverage}%
                                </Typography>
                              </Box>
                            </Grid>
                            <Grid size={{ xs: 6 }}>
                              <Box sx={{ textAlign: 'center', p: 1, border: '1px solid #e0e0e0', borderRadius: 1, m: 0.5 }}>
                                <Typography variant="body2" color="text.secondary">
                                  Printing
                                </Typography>
                                <Typography variant="body1" color="primary" sx={{ fontWeight: 'medium' }}>
                                  {formData.isColor ? 'Color' : 'B/W'} {formData.isDuplex ? '(Duplex)' : ''}
                                </Typography>
                              </Box>
                            </Grid>
                          </Grid>
                        </Box>
                      </Grid>
                    </Grid>
                  </Paper>
                </Grid>
                
                <Grid size={{ xs: 12, sm: 6, md: 4 }}>
                  <Paper elevation={3} sx={{ p: 3, textAlign: 'center', borderRadius: 2, height: '100%', bgcolor: '#f1f8fe', border: '1px solid #1976d2' }}>
                    <Typography variant="subtitle2" color="text.secondary" sx={{ mb: 1 }}>
                      Total Linear Meters
                    </Typography>
                    <Typography variant="h4" component="div" color="primary" sx={{ fontWeight: 'bold', mb: 1 }}>
                      {((Number(result.totalLinearMeters) || 0)).toFixed(2)} m
                    </Typography>
                    <Divider sx={{ my: 1 }} />
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mt: 1 }}>
                      <Typography variant="body2" color="text.secondary">
                        Meters per Copy:
                      </Typography>
                      <Typography variant="body1" color="primary" sx={{ fontWeight: 'bold' }}>
                        {(Number(result.linearMetersPerCopy) || 0).toFixed(2)} m
                      </Typography>
                    </Box>
                  </Paper>
                </Grid>
                
                <Grid size={{ xs: 12, sm: 6, md: 4 }}>
                  <Paper elevation={3} sx={{ p: 3, textAlign: 'center', borderRadius: 2, height: '100%', bgcolor: '#f1f8fe', border: '1px solid #1976d2' }}>
                    <Typography variant="subtitle2" color="text.secondary" sx={{ mb: 1 }}>
                      Total Pages
                    </Typography>
                    <Typography variant="h4" component="div" color="primary" sx={{ fontWeight: 'bold', mb: 1 }}>
                      {result.totalPages}
                    </Typography>
                    <Divider sx={{ my: 1 }} />
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mt: 1 }}>
                      <Typography variant="body2" color="text.secondary">
                        Pages per Copy:
                      </Typography>
                      <Typography variant="body1" color="primary" sx={{ fontWeight: 'bold' }}>
                        {result.totalPages / result.copies}
                      </Typography>
                    </Box>
                  </Paper>
                </Grid>
                
                <Grid size={{ xs: 12, sm: 6, md: 4 }}>
                  <Paper elevation={3} sx={{ p: 3, textAlign: 'center', borderRadius: 2, height: '100%', bgcolor: '#f1f8fe', border: '1px solid #1976d2' }}>
                    <Typography variant="subtitle2" color="text.secondary" sx={{ mb: 1 }}>
                      Total Ink Grams
                    </Typography>
                    <Typography variant="h4" component="div" color="primary" sx={{ fontWeight: 'bold', mb: 1 }}>
                      {((Number(result.totalInkGrams) || 0) / 1000).toFixed(3)} kg
                    </Typography>
                    <Divider sx={{ my: 1 }} />
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mt: 1 }}>
                      <Typography variant="body2" color="text.secondary">
                        Ink per Copy:
                      </Typography>
                      <Typography variant="body1" color="primary" sx={{ fontWeight: 'bold' }}>
                        {(Number(result.inkGramsPerCopy) || 0).toFixed(2)} g
                      </Typography>
                    </Box>
                  </Paper>
                </Grid>
                
                {/* Main data row */}
                
                {/* Printing times section */}
                <Grid size={{ xs: 12 }}>
                  <Paper elevation={3} sx={{ p: 3, borderRadius: 2, mt: 3, mb: 2, bgcolor: '#fff3e0', border: '1px solid #ff9800' }}>
                    <Typography variant="h6" gutterBottom sx={{ fontWeight: 'bold', color: '#e65100', mb: 2 }}>
                      Time
                    </Typography>
                    <Grid container spacing={2}>
                      <Grid size={{ xs: 12, md: 4 }}>
                        <Box sx={{ textAlign: 'center', p: 2 }}>
                          <Typography variant="subtitle2" color="text.secondary" sx={{ mb: 1 }}>
                            Printing Speed
                          </Typography>
                          <Typography variant="h4" component="div" sx={{ fontWeight: 'bold', color: '#e65100' }}>
                            {(Number(result.speedMetersPerMinute) || 0).toFixed(2)} m/min
                          </Typography>
                        </Box>
                      </Grid>
                      
                      <Grid size={{ xs: 12, md: 4 }}>
                        <Box sx={{ textAlign: 'center', p: 2 }}>
                          <Typography variant="subtitle2" color="text.secondary" sx={{ mb: 1 }}>
                            Time per Copy
                          </Typography>
                          <Typography variant="h4" component="div" sx={{ fontWeight: 'bold', color: '#e65100' }}>
                            {(Number(result.printTimeMinutesPerCopy) || 0).toFixed(2)} min
                          </Typography>
                        </Box>
                      </Grid>
                      
                      <Grid size={{ xs: 12, md: 4 }}>
                        <Box sx={{ textAlign: 'center', p: 2 }}>
                          <Typography variant="subtitle2" color="text.secondary" sx={{ mb: 1 }}>
                            Total Time
                          </Typography>
                          <Typography variant="h4" component="div" sx={{ fontWeight: 'bold', color: '#e65100' }}>
                            {(Number(result.totalPrintTimeMinutes) || 0).toFixed(2)} min
                          </Typography>
                        </Box>
                      </Grid>
                    </Grid>
                  </Paper>
                </Grid>
                
                {/* Second row - Detailed costs */}
                <Grid size={{ xs: 12 }}>
                  <Grid container spacing={2}>
                    {/* Costos del papel */}
                    <Grid size={{ xs: 12, md: 6 }}>
                      <Paper elevation={3} sx={{ p: 3, borderRadius: 2, height: '100%', bgcolor: '#e8f5e9', border: '1px solid #4caf50' }}>
                        <Typography variant="h6" gutterBottom sx={{ color: '#2e7d32', fontWeight: 'bold', mb: 2 }}>
                          Paper Costs
                        </Typography>
                        {result.paperRollId ? (
                          <>
                            <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                              <Typography variant="body1">Width:</Typography>
                              <Typography variant="body1" sx={{ fontWeight: 'bold' }}>{result.paperRollWidth} cm</Typography>
                            </Box>
                            <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                              <Typography variant="body1">Weight:</Typography>
                              <Typography variant="body1" sx={{ fontWeight: 'bold' }}>{result.paperRollGrammage} g/m²</Typography>
                            </Box>
                            <Divider sx={{ my: 2 }} />
                            <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                              <Typography variant="body1">Total Paper Cost:</Typography>
                              <Typography variant="body1" sx={{ fontWeight: 'bold' }}>{paperCost.toFixed(2)} €</Typography>
                            </Box>
                            <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                              <Typography variant="body1">Cost per Copy:</Typography>
                              <Typography variant="body1" sx={{ fontWeight: 'bold' }}>{paperCostPerCopy.toFixed(2)} €</Typography>
                            </Box>
                            <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                              <Typography variant="body1">Cost per Page (side):</Typography>
                              <Typography variant="body1" sx={{ fontWeight: 'bold' }}>{paperCostPerPage.toFixed(4)} €</Typography>
                            </Box>
                          </>
                        ) : (
                          <Typography variant="body1" sx={{ color: 'text.secondary', fontStyle: 'italic', textAlign: 'center' }}>
                            Select a paper roll to view paper costs
                          </Typography>
                        )}
                      </Paper>
                    </Grid>
                    
                    {/* Costos de tinta */}
                    <Grid size={{ xs: 12, md: 6 }}>
                      <Paper elevation={3} sx={{ p: 3, borderRadius: 2, height: '100%', bgcolor: '#e8f5e9', border: '1px solid #4caf50' }}>
                        <Typography variant="h6" gutterBottom sx={{ color: '#2e7d32', fontWeight: 'bold', mb: 2 }}>
                          Ink Costs
                        </Typography>
                        <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                          <Typography variant="body1">Coverage:</Typography>
                          <Typography variant="body1" sx={{ fontWeight: 'bold' }}>{formData.coverage}%</Typography>
                        </Box>
                        <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                          <Typography variant="body1">Type:</Typography>
                          <Typography variant="body1" sx={{ fontWeight: 'bold' }}>{formData.isColor ? 'Color' : 'B/W'}</Typography>
                        </Box>
                        <Divider sx={{ my: 2 }} />
                        <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                          <Typography variant="body1">Total Ink Cost:</Typography>
                          <Typography variant="body1" sx={{ fontWeight: 'bold' }}>{totalInkCost.toFixed(2)} €</Typography>
                        </Box>
                        <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                          <Typography variant="body1">Cost per Copy:</Typography>
                          <Typography variant="body1" sx={{ fontWeight: 'bold' }}>{inkCostPerCopy.toFixed(2)} €</Typography>
                        </Box>
                        <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                          <Typography variant="body1">Cost per Page (side):</Typography>
                          <Typography variant="body1" sx={{ fontWeight: 'bold' }}>{inkCostPerPage.toFixed(4)} €</Typography>
                        </Box>
                      </Paper>
                    </Grid>
                    
                    {/* Costo total */}
                    <Grid size={{ xs: 12 }}>
                      <Paper elevation={3} sx={{ p: 3, borderRadius: 2, mt: 2, bgcolor: '#e8f5e9', border: '1px solid #4caf50' }}>
                        <Typography variant="h6" gutterBottom sx={{ fontWeight: 'bold', color: '#2e7d32', mb: 2 }}>
                          Total Costs
                        </Typography>
                        <Grid container spacing={2}>
                          <Grid size={{ xs: 12, md: 4 }}>
                            <Box sx={{ textAlign: 'center', p: 2 }}>
                              <Typography variant="subtitle2" color="text.secondary" sx={{ mb: 1 }}>
                                Total Cost
                              </Typography>
                              <Typography variant="h4" component="div" sx={{ fontWeight: 'bold', color: '#2e7d32' }}>
                                {totalCost.toFixed(2)} €
                              </Typography>
                            </Box>
                          </Grid>
                          
                          <Grid size={{ xs: 12, md: 4 }}>
                            <Box sx={{ textAlign: 'center', p: 2 }}>
                              <Typography variant="subtitle2" color="text.secondary" sx={{ mb: 1 }}>
                                Cost per Copy
                              </Typography>
                              <Typography variant="h4" component="div" sx={{ fontWeight: 'bold', color: '#2e7d32' }}>
                                {totalCostPerCopy.toFixed(2)} €
                              </Typography>
                            </Box>
                          </Grid>
                          
                          <Grid size={{ xs: 12, md: 4 }}>
                            <Box sx={{ textAlign: 'center', p: 2 }}>
                              <Typography variant="subtitle2" color="text.secondary" sx={{ mb: 1 }}>
                                Cost per Page
                              </Typography>
                              <Typography variant="h4" component="div" sx={{ fontWeight: 'bold', color: '#2e7d32' }}>
                                {totalCostPerPage.toFixed(4)} €
                              </Typography>
                            </Box>
                          </Grid>
                        </Grid>
                      </Paper>
                    </Grid>
                  </Grid>
                </Grid>
              </Grid>
              
              {renderCharts()}
            </Box>
          )}
        </CardContent>
      </Card>
    </Box>
  );
};
