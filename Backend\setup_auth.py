#!/usr/bin/env python3
"""
Script de configuración para el sistema de autenticación OAuth 2.0
"""

import json
import os
import sys
from services.auth_service import AuthService

def main():
    print("=== Configuración del Sistema de Autenticación ===\n")
    
    # Verificar que existe el archivo de configuración
    config_file = "config/email_config.json"
    if not os.path.exists(config_file):
        print(f"❌ Error: No se encontró el archivo {config_file}")
        print("Por favor, asegúrate de que el archivo existe y está configurado correctamente.")
        return False
    
    # Cargar configuración
    try:
        with open(config_file, 'r') as f:
            config = json.load(f)
    except Exception as e:
        print(f"❌ Error al cargar la configuración: {e}")
        return False
    
    # Verificar que el client_secret está configurado
    client_secret = config.get("oauth2", {}).get("client_secret", "")
    if client_secret == "REPLACE_WITH_YOUR_CLIENT_SECRET" or not client_secret:
        print("❌ Error: El client_secret no está configurado.")
        print("Por favor, edita el archivo config/email_config.json y reemplaza")
        print("'REPLACE_WITH_YOUR_CLIENT_SECRET' con tu client secret real de Google Cloud Console.")
        return False
    
    print("✅ Archivo de configuración encontrado y válido")
    
    # Inicializar servicio de autenticación
    try:
        auth_service = AuthService()
        print("✅ Servicio de autenticación inicializado")
    except Exception as e:
        print(f"❌ Error al inicializar el servicio: {e}")
        return False
    
    # Verificar si ya hay tokens OAuth configurados
    tokens_file = "data/oauth_tokens.json"
    if os.path.exists(tokens_file):
        try:
            with open(tokens_file, 'r') as f:
                tokens = json.load(f)
            if tokens and "access_token" in tokens:
                print("✅ Tokens OAuth ya configurados")
                
                # Preguntar si quiere reconfigurar
                response = input("\n¿Deseas reconfigurar los tokens OAuth? (y/N): ").strip().lower()
                if response not in ['y', 'yes', 'sí', 'si']:
                    print("✅ Configuración completada. El sistema está listo para usar.")
                    return True
        except:
            pass
    
    # Obtener URL de autorización
    try:
        auth_url = auth_service.get_oauth_authorization_url()
        print("\n🔗 URL de autorización OAuth:")
        print(f"   {auth_url}")
        print("\n📋 Pasos a seguir:")
        print("1. Copia la URL de arriba y ábrela en tu navegador")
        print("2. Inicia sesión con <NAME_EMAIL>")
        print("3. Autoriza la aplicación")
        print("4. Copia el código de autorización de la URL de callback")
        print("5. Pégalo aquí abajo")
        
        # Solicitar código de autorización
        print("\n" + "="*60)
        auth_code = input("Ingresa el código de autorización: ").strip()
        
        if not auth_code:
            print("❌ No se proporcionó código de autorización")
            return False
        
        # Procesar callback
        print("\n🔄 Procesando código de autorización...")
        success = auth_service.handle_oauth_callback(auth_code)
        
        if success:
            print("✅ ¡Configuración OAuth completada exitosamente!")
            print("✅ El sistema de autenticación está listo para usar.")
            
            # Probar envío de email
            test_email = input("\n¿Deseas probar el envío de email? Ingresa un email de prueba (o presiona Enter para omitir): ").strip()
            if test_email:
                print(f"\n🔄 Enviando código de prueba a {test_email}...")
                try:
                    if auth_service.send_verification_code(test_email):
                        print("✅ ¡Email de prueba enviado exitosamente!")
                    else:
                        print("❌ Error al enviar email de prueba")
                except Exception as e:
                    print(f"❌ Error al enviar email de prueba: {e}")
            
            return True
        else:
            print("❌ Error al procesar el código de autorización")
            return False
            
    except Exception as e:
        print(f"❌ Error durante la configuración OAuth: {e}")
        return False

def check_dependencies():
    """Verificar que las dependencias están instaladas"""
    try:
        import google.auth
        import google.oauth2
        import googleapiclient
        import jose
        return True
    except ImportError as e:
        print(f"❌ Error: Dependencia faltante: {e}")
        print("Por favor, instala las dependencias con: pip install -r requirements.txt")
        return False

if __name__ == "__main__":
    print("Verificando dependencias...")
    if not check_dependencies():
        sys.exit(1)
    
    success = main()
    
    if success:
        print("\n🎉 ¡Configuración completada exitosamente!")
        print("\nPuedes iniciar el servidor con: python main.py")
        print("Y probar la autenticación en: http://localhost:3006/docs")
    else:
        print("\n❌ La configuración no se completó correctamente.")
        print("Por favor, revisa los errores de arriba y vuelve a intentar.")
        sys.exit(1)
