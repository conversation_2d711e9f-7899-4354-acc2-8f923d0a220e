// Application configuration
// Use environment variable if available, otherwise fallback to localhost
const isDevelopment = import.meta.env.DEV;
const defaultApiUrl = isDevelopment ? 'http://localhost:3006' : 'https://api.atexco.triky.app';

export const API_URL = import.meta.env.VITE_API_URL || defaultApiUrl;
export const API_BASE_URL = API_URL; // Alias for consistency

// Debug info
console.log('🔧 Config Info:', {
  isDevelopment,
  VITE_API_URL: import.meta.env.VITE_API_URL,
  API_URL,
  mode: import.meta.env.MODE
});
