#!/usr/bin/env python3
"""
Script para probar el envío de emails directamente
"""

from services.auth_service import AuthService
import json
import os
from datetime import datetime

def test_email_sending():
    print("🧪 Prueba de Envío de Emails")
    print("=" * 50)
    
    try:
        # Crear servicio
        print("🔧 Inicializando AuthService...")
        auth_service = AuthService()
        print("✅ AuthService inicializado")
        
        # Verificar configuración
        print("\n📋 Verificando configuración...")
        config = auth_service.config
        email_config = config.get('email', {})
        oauth_config = config.get('oauth2', {})
        
        print(f"   📧 From Email: {email_config.get('from_email', 'N/A')}")
        print(f"   👤 From Name: {email_config.get('from_name', 'N/A')}")
        print(f"   🔑 Client ID: {oauth_config.get('client_id', 'N/A')[:30]}...")
        print(f"   🔄 Redirect URI: {oauth_config.get('redirect_uri', 'N/A')}")
        
        # Verificar tokens OAuth
        print("\n🔑 Verificando tokens OAuth...")
        tokens = auth_service._load_oauth_tokens()
        
        if not tokens:
            print("❌ No se encontraron tokens OAuth")
            print("   Ejecuta: python quick_oauth_setup.py")
            return False
        
        if not tokens.get('access_token'):
            print("❌ Token de acceso no válido")
            return False
        
        print("✅ Tokens OAuth encontrados")
        
        # Verificar expiración
        if 'expires_at' in tokens:
            try:
                expires_at = datetime.fromisoformat(tokens['expires_at'])
                now = datetime.now()
                if expires_at <= now:
                    print("⚠️  Tokens OAuth expirados")
                else:
                    print(f"✅ Tokens válidos hasta: {expires_at}")
            except:
                print("⚠️  No se pudo verificar expiración")
        
        # Probar servicio Gmail
        print("\n🔧 Probando servicio Gmail...")
        try:
            gmail_service = auth_service._get_gmail_service()
            print("✅ Servicio Gmail obtenido correctamente")
        except Exception as e:
            print(f"❌ Error obteniendo servicio Gmail: {e}")
            return False
        
        # Solicitar email de prueba
        print("\n📧 Prueba de envío de email")
        print("-" * 30)
        
        test_email = input("Ingresa un email para probar: ").strip()
        
        if not test_email:
            print("❌ No se proporcionó email")
            return False
        
        print(f"\n🚀 Enviando código de verificación a: {test_email}")
        print("📊 Logs detallados:")
        print("-" * 40)
        
        # Enviar email con logs detallados
        success = auth_service.send_verification_code(test_email)
        
        print("-" * 40)
        
        if success:
            print("✅ Función send_verification_code retornó True")
            
            # Mostrar código generado
            try:
                with open('data/verification_codes.json', 'r') as f:
                    codes = json.load(f)
                
                # Buscar el código más reciente para este email
                latest_code = None
                for code_data in reversed(codes):
                    if code_data.get('email') == test_email:
                        latest_code = code_data
                        break
                
                if latest_code:
                    print(f"🔢 Código generado: {latest_code['code']}")
                    print(f"⏰ Expira: {latest_code['expires_at']}")
                else:
                    print("⚠️  No se encontró código en el archivo")
                    
            except Exception as e:
                print(f"⚠️  Error leyendo códigos: {e}")
            
            print("\n🔍 Verifica tu bandeja de entrada (y spam)")
            print("📧 El email debería llegar desde: <EMAIL>")
            
            return True
        else:
            print("❌ Función send_verification_code retornó False")
            return False
            
    except Exception as e:
        print(f"❌ Error durante la prueba: {e}")
        import traceback
        traceback.print_exc()
        return False

def check_system_status():
    """Verificar estado del sistema"""
    print("\n📊 Estado del Sistema")
    print("-" * 30)
    
    # Verificar archivos
    files_to_check = [
        'config/email_config.json',
        'data/oauth_tokens.json',
        'data/verification_codes.json'
    ]
    
    for file_path in files_to_check:
        if os.path.exists(file_path):
            size = os.path.getsize(file_path)
            print(f"✅ {file_path} ({size} bytes)")
        else:
            print(f"❌ {file_path} (no existe)")
    
    # Verificar importaciones
    try:
        from google.oauth2.credentials import Credentials
        from googleapiclient.discovery import build
        print("✅ Librerías Google importadas correctamente")
    except ImportError as e:
        print(f"❌ Error importando librerías Google: {e}")
    
    try:
        from jose import jwt
        print("✅ Librería jose importada correctamente")
    except ImportError as e:
        print(f"❌ Error importando jose: {e}")

if __name__ == "__main__":
    check_system_status()
    
    print("\n" + "=" * 50)
    success = test_email_sending()
    
    if success:
        print("\n🎉 ¡Prueba completada!")
        print("Si no recibiste el email, revisa los logs de arriba.")
    else:
        print("\n❌ La prueba falló.")
        print("Revisa los errores de arriba para diagnosticar el problema.")
    
    input("\nPresiona Enter para salir...")
