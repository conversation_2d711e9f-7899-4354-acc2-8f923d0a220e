import axios from 'axios';
import { API_BASE_URL } from '../config';

export interface EmailRequest {
  email: string;
}

export interface VerificationRequest {
  email: string;
  code: string;
}

export interface AuthResponse {
  success: boolean;
  message: string;
  token?: string;
  expires_at?: string;
}

export interface TokenVerificationResponse {
  valid: boolean;
  email: string;
  message: string;
}

class AuthService {
  private readonly TOKEN_KEY = 'atexco_auth_token';
  private readonly EMAIL_KEY = 'atexco_user_email';

  // Get stored token
  getToken(): string | null {
    return localStorage.getItem(this.TOKEN_KEY);
  }

  // Get stored email
  getEmail(): string | null {
    return localStorage.getItem(this.EMAIL_KEY);
  }

  // Store token and email
  setAuth(token: string, email: string): void {
    localStorage.setItem(this.TOKEN_KEY, token);
    localStorage.setItem(this.EMAIL_KEY, email);
    this.setupAxiosInterceptor();
  }

  // Clear stored auth data
  clearAuth(): void {
    localStorage.removeItem(this.TOKEN_KEY);
    localStorage.removeItem(this.EMAIL_KEY);
    this.setupAxiosInterceptor();
  }

  // Check if user is authenticated
  isAuthenticated(): boolean {
    return !!this.getToken();
  }

  // Setup axios interceptor to include auth token
  setupAxiosInterceptor(): void {
    // Add request interceptor to include auth token
    axios.interceptors.request.use(
      (config) => {
        const token = this.getToken();
        if (token) {
          config.headers.Authorization = `Bearer ${token}`;
        }
        return config;
      },
      (error) => {
        return Promise.reject(error);
      }
    );

    // Response interceptor to handle 401 errors
    axios.interceptors.response.use(
      (response) => response,
      (error) => {
        if (error.response?.status === 401) {
          this.clearAuth();
          // Redirect to home page instead of /login since we handle auth in the app
          window.location.reload();
        }
        return Promise.reject(error);
      }
    );
  }

  // Send verification code to email
  async sendVerificationCode(email: string): Promise<AuthResponse> {
    try {
      const response = await axios.post<AuthResponse>(`${API_BASE_URL}/auth/send-code`, {
        email
      });
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.detail || 'Failed to send verification code');
    }
  }

  // Verify code and get token
  async verifyCode(email: string, code: string): Promise<AuthResponse> {
    try {
      const response = await axios.post<AuthResponse>(`${API_BASE_URL}/auth/verify-code`, {
        email,
        code
      });

      if (response.data.success && response.data.token) {
        this.setAuth(response.data.token, email);
      }

      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.detail || 'Failed to verify code');
    }
  }

  // Verify current token
  async verifyToken(): Promise<boolean> {
    try {
      const token = this.getToken();
      if (!token) return false;

      const response = await axios.get<TokenVerificationResponse>(`${API_BASE_URL}/auth/verify-token`);
      return response.data.valid;
    } catch (error) {
      this.clearAuth();
      return false;
    }
  }

  // Logout
  logout(): void {
    this.clearAuth();
  }
}

// Create singleton instance
const authService = new AuthService();

// Setup interceptor on app start
authService.setupAxiosInterceptor();

export default authService;
