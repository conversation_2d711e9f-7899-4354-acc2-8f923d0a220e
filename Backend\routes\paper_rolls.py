from fastapi import APIRouter, HTTPException, Depends
from typing import List
from models.paper_roll import PaperRoll, PaperRollCreate, PaperRollUpdate
from services.paper_roll_service import PaperRollService

router = APIRouter(
    prefix="/paper-rolls",
    tags=["paper-rolls"],
)

def get_paper_roll_service():
    return PaperRollService()

@router.get("/", response_model=List[PaperRoll])
def get_all_paper_rolls(service: PaperRollService = Depends(get_paper_roll_service)):
    return service.get_all()

@router.get("/{roll_id}", response_model=PaperRoll)
def get_paper_roll(roll_id: str, service: PaperRollService = Depends(get_paper_roll_service)):
    paper_roll = service.get_by_id(roll_id)
    if paper_roll is None:
        raise HTTPException(status_code=404, detail="Bobina no encontrada")
    return paper_roll

@router.post("", response_model=PaperRoll)
def create_paper_roll(paper_roll: PaperRollCreate, service: PaperRollService = Depends(get_paper_roll_service)):
    return service.create(paper_roll)

@router.put("/{roll_id}", response_model=PaperRoll)
def update_paper_roll(roll_id: str, paper_roll: PaperRollUpdate, service: PaperRollService = Depends(get_paper_roll_service)):
    updated_roll = service.update(roll_id, paper_roll)
    if updated_roll is None:
        raise HTTPException(status_code=404, detail="Bobina no encontrada")
    return updated_roll

@router.delete("/{roll_id}")
def delete_paper_roll(roll_id: str, service: PaperRollService = Depends(get_paper_roll_service)):
    success = service.delete(roll_id)
    if not success:
        raise HTTPException(status_code=404, detail="Bobina no encontrada")
    return {"message": "Bobina eliminada correctamente"}

# Removed seed endpoint as we're now using the data file directly
