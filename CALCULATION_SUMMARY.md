```markdown
# Resumen del Funcionamiento de los Cálculos

Este documento explica cómo la API calcula los diferentes costos y métricas de producción basándose en las entradas del usuario y las configuraciones de la máquina y el papel.

## 1. Datos de Entrada Principales

La calculadora recibe la siguiente información del usuario:

*   **Modelo de Máquina:** Selecciona la configuración específica (velocidad, consumo de tinta, precio de tintas).
*   **Dimensiones de Página (mm):** Ancho y alto del producto final.
*   **Páginas por Copia:** **Número total de caras impresas** para una copia del trabajo (ej: un libro de 100 páginas impreso a doble cara tiene 100 "páginas por copia").
*   **Impresión a Doble Cara (Duplex):** Indicador (Sí/No). Ya no afecta directamente al cálculo del área total, ya que "Páginas por Copia" representa las caras.
*   **Cobertura de Tinta (%):** Porcentaje de cobertura promedio por cara.
*   **Impresión a Color:** Indicador (Sí/No). Afecta qué precio de tinta se usa.
*   **Número de Copias:** Cantidad total de copias idénticas a producir.
*   **Rollo de Papel:** Selecciona el tipo de papel (ancho, largo, precio).

## 2. Cálculos de Papel (Metros Lineales y Costo)

*   **Productos por Ancho:** Calcula cuántos productos (páginas) caben a lo ancho del rollo de papel seleccionado.
    *   `Productos por Ancho = Ancho del Rollo (mm) // Ancho de Página (mm)`
*   **Total de Caras Impresas:** Calcula el número total de caras a imprimir para todo el trabajo.
    *   `Total Caras = Páginas por Copia * Número de Copias`
*   **Metros Lineales por Fila de Productos:** Calcula cuántos metros de largo se necesitan para imprimir una "fila" de productos a lo ancho del rollo.
    *   `Metros por Fila = Alto de Página (mm) / 1000`
*   **Total Metros Lineales:** Calcula el largo total de papel necesario. Se asegura de redondear hacia arriba (`ceil`) para tener suficientes filas completas.
    *   `Total Metros = ceil(Total Caras / Productos por Ancho) * Metros por Fila`
*   **Metros Lineales por Copia:**
    *   `Metros por Copia = Total Metros / Número de Copias`
*   **Costo Total del Papel (€):** Calcula el costo basado en los metros usados y el precio por metro del rollo.
    *   `Precio por Metro = Precio del Rollo / Largo del Rollo (m)`
    *   `Costo Total Papel = Total Metros * Precio por Metro`
*   **Costo de Papel por Copia (€):**
    *   `Costo Papel por Copia = Costo Total Papel / Número de Copias`
*   **Costo de Papel por Cara (€):**
    *   `Costo Papel por Cara = Costo Total Papel / Total Caras`

## 3. Cálculos de Tinta (Peso y Costo)

*   **Área de una Cara (m²):** Calcula el área de una única cara impresa.
    *   `Área Cara = (Ancho de Página / 1000) * (Alto de Página / 1000)`
*   **Área Total por Copia (m²):** Multiplica el área de una cara por el número total de caras por copia.
    *   `Área por Copia = Área Cara * Páginas por Copia`
*   **Fracción de Cobertura:** Convierte el porcentaje de cobertura a una fracción decimal.
    *   `Fracción Cobertura = Cobertura (%) / 100`
*   **Consumo de Tinta (Definición):** Utiliza el valor `ink_consumption` de la configuración de la máquina, que representa **gramos por cada 1% de cobertura por m²**.
*   **Gramos de Tinta por Copia:** Calcula cuántos gramos de tinta consume una copia completa.
    *   `Gramos por Copia = Área por Copia * (Fracción Cobertura * 100) * ink_consumption`
*   **Total Gramos de Tinta:** Calcula el peso total de tinta para todo el trabajo.
    *   `Total Gramos = Gramos por Copia * Número de Copias`
*   **Precio Efectivo de Tinta (€/kg):** Determina qué precio usar basado en si la impresión es a color.
    *   Si es Color: `Precio Efectivo = (0.25 * Precio Tinta K) + (0.75 * Precio Tinta CMY)`
    *   Si no es Color: `Precio Efectivo = Precio Tinta K` (o CMY si K no está definido)
*   **Costo de Tinta por Copia (€):**
    *   `Costo Tinta por Copia = (Gramos por Copia / 1000) * Precio Efectivo`
*   **Costo Total de Tinta (€):**
    *   `Costo Total Tinta = Costo Tinta por Copia * Número de Copias`
*   **Costo de Tinta por Cara (Simplex) (€):** Calcula el costo de tinta específico para imprimir una sola cara (útil para comparaciones).
    *   `Gramos Tinta por Cara = Área Cara * (Fracción Cobertura * 100) * ink_consumption`
    *   `Costo Tinta por Cara = (Gramos Tinta por Cara / 1000) * Precio Efectivo`

## 4. Cálculo de Tiempo

*   **Velocidad de la Máquina (m/min):** Obtiene la velocidad máxima de la configuración de la máquina.
*   **Tiempo Total de Impresión (minutos):** Calcula cuánto tiempo tardará en imprimirse todo el papel necesario.
    *   `Tiempo Total = Total Metros Lineales / Velocidad Máquina`
*   **Tiempo por Copia (minutos):**
    *   `Tiempo por Copia = Tiempo Total / Número de Copias`

## 5. Resultados Finales

La API devuelve todos estos valores calculados (costos totales, por copia, por cara, pesos, metros, tiempos) para que se muestren en la interfaz de usuario.
```
