import React from 'react';
import AppBar from '@mui/material/AppBar';
import Toolbar from '@mui/material/Toolbar';
import Typography from '@mui/material/Typography';
import Container from '@mui/material/Container';
import Box from '@mui/material/Box';
import PrintIcon from '@mui/icons-material/Print';

export const Header: React.FC = () => {
  return (
    <AppBar position="static" color="primary" sx={{ width: '100%' }}>
      <Toolbar sx={{ display: 'flex', justifyContent: 'center' }}>
        <Container maxWidth="lg" sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
          <Box sx={{ display: 'flex', alignItems: 'center', width: '100%', maxWidth: '1200px' }}>
            <PrintIcon sx={{ mr: 2 }} />
            <Typography variant="h6" component="div">
              Atexco Vega Calculator
            </Typography>
          </Box>
        </Container>
      </Toolbar>
    </AppBar>
  );
};
