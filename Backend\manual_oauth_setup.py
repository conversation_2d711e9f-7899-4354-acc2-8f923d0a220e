#!/usr/bin/env python3
"""
Manual OAuth setup - extracts code from browser manually
"""

import json
import webbrowser
from services.auth_service import AuthService

def manual_oauth_setup():
    print("=== Configuración Manual de OAuth 2.0 ===\n")
    
    try:
        auth_service = AuthService()
        
        # Get authorization URL
        auth_url = auth_service.get_oauth_authorization_url()
        
        print("🔗 URL de autorización generada:")
        print(f"   {auth_url}")
        print()
        
        # Try to open in browser
        try:
            webbrowser.open(auth_url)
            print("✅ URL abierta en el navegador")
        except:
            print("❌ No se pudo abrir automáticamente")
            print("   Copia y pega la URL manualmente en tu navegador")
        
        print("\n" + "="*60)
        print("📋 INSTRUCCIONES:")
        print("1. Inicia sesió<NAME_EMAIL>")
        print("2. Autoriza la aplicación")
        print("3. Después de autorizar, serás redirigido a una página de error")
        print("4. ¡NO TE PREOCUPES POR EL ERROR!")
        print("5. Mira la URL en la barra de direcciones del navegador")
        print("6. Busca la parte que dice 'code=' en la URL")
        print("7. Copia TODO lo que viene después de 'code=' hasta el siguiente '&' (si lo hay)")
        print("="*60)
        
        print("\nEjemplo de URL de callback:")
        print("https://atexco.triky.app/rest/oauth2-credential/callback?code=4/0AanRRvs1234567890&scope=...")
        print("                                                            ^^^^^^^^^^^^^^^^")
        print("                                                            COPIA ESTA PARTE")
        
        # Get code from user
        print("\n" + "-"*50)
        code = input("Pega el código de autorización aquí: ").strip()
        
        if not code:
            print("❌ No se proporcionó código")
            return False
        
        # Clean the code (remove any extra parameters)
        if '&' in code:
            code = code.split('&')[0]
        if '?' in code:
            code = code.split('?')[-1]
        if code.startswith('code='):
            code = code[5:]
        
        print(f"\n🔄 Procesando código: {code[:20]}...")
        
        # Process the code
        success = auth_service.handle_oauth_callback(code)
        
        if success:
            print("✅ ¡OAuth configurado exitosamente!")
            
            # Test sending an email
            test_email = input("\n¿Quieres probar enviando un email? Ingresa un email de prueba (o Enter para omitir): ").strip()
            if test_email:
                print(f"\n🔄 Enviando email de prueba a {test_email}...")
                try:
                    result = auth_service.send_verification_code(test_email)
                    if result:
                        print("✅ ¡Email enviado exitosamente!")
                        print("🔍 Revisa la bandeja de entrada (y spam) del email de destino")
                    else:
                        print("❌ Error al enviar email")
                except Exception as e:
                    print(f"❌ Error al enviar email: {e}")
            
            return True
        else:
            print("❌ Error al procesar el código OAuth")
            return False
            
    except Exception as e:
        print(f"❌ Error durante la configuración: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = manual_oauth_setup()
    
    if success:
        print("\n🎉 ¡Configuración completada!")
        print("Ahora puedes usar el sistema de autenticación con envío real de emails.")
    else:
        print("\n❌ La configuración no se completó.")
        print("Puedes intentar de nuevo ejecutando este script.")
