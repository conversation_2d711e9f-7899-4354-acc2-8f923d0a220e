from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
import uvicorn
import os

# Import routers
from routes.base import router as base_router
from routes.calculator import router as calculator_router
from routes.paper_rolls import router as paper_rolls_router
from routes.machine_config import router as machine_config_router
from routes.auth import router as auth_router
from routes.test_auth import router as test_auth_router

app = FastAPI(title="Atexco Vega Calculator API")

# Enable CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include routers
app.include_router(base_router)
app.include_router(test_auth_router)
app.include_router(auth_router)
app.include_router(calculator_router)
app.include_router(paper_rolls_router)
app.include_router(machine_config_router)

if __name__ == "__main__":
    uvicorn.run("main:app", host="0.0.0.0", port=3006, reload=True)
