import React, { useState, useEffect } from 'react';
import { 
  Container, 
  Typography, 
  Paper, 
  TextField, 
  Button, 
  FormControl, 
  InputLabel, 
  Select, 
  MenuItem, 
  Box,
  Snackbar,
  Alert,
  SelectChangeEvent
} from '@mui/material';
import { MachineConfig as MachineConfigType } from '../models/MachineConfig';
import { 
  getMachineConfigs, 
  getMachineConfigByModel, 
  updateMachineConfig 
} from '../services/machineConfigService';

const A4_AREA_M2 = 0.06237; // 210mm * 297mm

const MachineConfig: React.FC = () => {
  const [machineConfigs, setMachineConfigs] = useState<MachineConfigType[]>([]);
  const [selectedModel, setSelectedModel] = useState<string>('');
  const [currentConfig, setCurrentConfig] = useState<MachineConfigType | null>(null);
  const [formData, setFormData] = useState({
    price: 0,
    max_speed: 0,
    ink_price_k: 0,
    ink_price_cmy: 0,
    ink_consumption: 0
  });
  const [assumedCoverage, setAssumedCoverage] = useState<number>(5);
  const [snackbar, setSnackbar] = useState({
    open: false,
    message: '',
    severity: 'success' as 'success' | 'error'
  });

  useEffect(() => {
    const fetchMachineConfigs = async () => {
      try {
        const configs = await getMachineConfigs();
        setMachineConfigs(configs);
        if (configs.length > 0) {
          setSelectedModel(configs[0].model);
        }
      } catch (error) {
        console.error('Error fetching machine configurations:', error);
        setSnackbar({
          open: true,
          message: 'Error loading machine configurations',
          severity: 'error'
        });
      }
    };

    fetchMachineConfigs();
  }, []);

  useEffect(() => {
    const fetchMachineConfig = async () => {
      if (!selectedModel) return;
      
      try {
        const config = await getMachineConfigByModel(selectedModel);
        setCurrentConfig(config);
        setFormData({
          price: config.price,
          max_speed: config.max_speed,
          ink_price_k: config.ink_price_k || 0,
          ink_price_cmy: config.ink_price_cmy || 0,
          ink_consumption: config.ink_consumption
        });
      } catch (error) {
        console.error(`Error fetching machine configuration for model ${selectedModel}:`, error);
        setCurrentConfig(null);
        setFormData({
          price: 0,
          max_speed: 0,
          ink_price_k: 0,
          ink_price_cmy: 0,
          ink_consumption: 0
        });
        setSnackbar({
          open: true,
          message: `Error loading configuration for model ${selectedModel}`,
          severity: 'error'
        });
      }
    };

    fetchMachineConfig();
  }, [selectedModel]);

  const handleModelChange = (event: SelectChangeEvent) => {
    setSelectedModel(event.target.value as string);
  };

  const handleInputChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = event.target;
    const numericValue = value === '' ? '' : parseFloat(value);
    setFormData({
      ...formData,
      [name]: numericValue === '' ? '' : (isNaN(numericValue) ? formData[name as keyof typeof formData] : numericValue)
    });
  };

  const handleCoverageChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const value = parseFloat(event.target.value);
    setAssumedCoverage(isNaN(value) || value < 0 ? 0 : value);
  };

  const handleSubmit = async (event: React.FormEvent) => {
    event.preventDefault();
    
    if (!currentConfig) return;
    
    try {
      const price = Number(formData.price) || 0;
      const max_speed = Number(formData.max_speed) || 0;
      const ink_price_k = Number(formData.ink_price_k) || 0;
      const ink_price_cmy = Number(formData.ink_price_cmy) || 0;
      const ink_consumption = Number(formData.ink_consumption) || 0;

      const updatedData = {
        id: currentConfig.id,
        model: currentConfig.model,
        price: price,
        max_speed: max_speed,
        ink_price_k: ink_price_k,
        ink_price_cmy: ink_price_cmy,
        ink_consumption: ink_consumption
      };
      
      const updatedConfig = await updateMachineConfig(currentConfig.id, updatedData);
      
      setCurrentConfig(updatedConfig);
      setFormData({
        price: updatedConfig.price,
        max_speed: updatedConfig.max_speed,
        ink_price_k: updatedConfig.ink_price_k || 0,
        ink_price_cmy: updatedConfig.ink_price_cmy || 0,
        ink_consumption: updatedConfig.ink_consumption
      });

      setSnackbar({
        open: true,
        message: 'Configuration updated successfully',
        severity: 'success'
      });
    } catch (error) {
      console.error('Error updating machine configuration:', error);
      setSnackbar({
        open: true,
        message: 'Error updating configuration',
        severity: 'error'
      });
    }
  };

  const handleCloseSnackbar = () => {
    setSnackbar({
      ...snackbar,
      open: false
    });
  };

  const inkConsumption = formData.ink_consumption;
  const inkPriceK = formData.ink_price_k;
  const inkPriceCMY = formData.ink_price_cmy;

  let pagesPerKgText = '';
  let costPerPageText = '';
  let costPerM2Text = '';

  if (
    selectedModel &&
    typeof inkConsumption === 'number' && inkConsumption > 0 &&
    typeof assumedCoverage === 'number' && assumedCoverage > 0 &&
    typeof inkPriceK === 'number' && inkPriceK >= 0 &&
    typeof inkPriceCMY === 'number' && inkPriceCMY >= 0
  ) {
    // Calculate weighted average ink price (25% K, 75% CMY)
    const avgInkPrice = (0.25 * inkPriceK) + (0.75 * inkPriceCMY);

    const inkGramsPerPage = A4_AREA_M2 * assumedCoverage * inkConsumption;
    const inkGramsPerM2 = assumedCoverage * inkConsumption; // Grams per m² at assumed coverage

    if (avgInkPrice > 0) { // Check average price to avoid division by zero issues implicitly
      // Pages per Kg calculation (using average price for yield? Or keep simple grams? Let's use simple grams for yield)
      if (inkGramsPerPage > 0) {
        const pagesPerKg = Math.round(1000 / inkGramsPerPage);
        pagesPerKgText = `Yield: ≈ ${pagesPerKg.toLocaleString()} A4 pages/kg (@ ${assumedCoverage}% coverage)`;
      } else {
        pagesPerKgText = '';
      }

      // Cost per Page calculation (CMYK combined)
      const costPerPageCMYK = (inkGramsPerPage / 1000) * avgInkPrice;
      costPerPageText = `Ink Cost (A4): ≈ €${costPerPageCMYK.toFixed(4)}/page (CMYK)`;
      
      // Cost per m² calculation (CMYK combined)
      const costPerM2_CMYK = (inkGramsPerM2 / 1000) * avgInkPrice;
      costPerM2Text = `Ink Cost (m²): ≈ €${costPerM2_CMYK.toFixed(4)}/m² (CMYK @ ${assumedCoverage}% coverage)`;

    } else {
      // Reset texts if calculation isn't possible (e.g., zero prices)
      pagesPerKgText = inkGramsPerPage > 0 ? `Yield: Cannot calculate pages/kg (zero avg ink price)` : ''; // Handle yield separately if grams > 0 but price = 0
      costPerPageText = '';
      costPerM2Text = '';
    }
  } else if (selectedModel) {
    pagesPerKgText = ''; 
    costPerPageText = ''; 
    costPerM2Text = '';
  }

  const isFormDisabled = !currentConfig;

  return (
    <Container maxWidth="md" sx={{ mt: 4, mb: 4 }}>
      <Paper elevation={3} sx={{ p: 4, borderRadius: 2 }}>
        <Typography variant="h4" component="h1" gutterBottom sx={{ color: '#1976d2', fontWeight: 'bold', mb: 3 }}>
          Machine Configuration
        </Typography>
        
        <Box component="form" onSubmit={handleSubmit} sx={{ mt: 3 }}>
          <Box sx={{ display: 'flex', flexWrap: 'wrap', margin: 1 }}>
            <Box sx={{ padding: 1, width: { xs: '100%', sm: '50%' } }}>
              <FormControl fullWidth>
                <InputLabel id="model-select-label">Machine Model</InputLabel>
                <Select
                  labelId="model-select-label"
                  id="model-select"
                  value={selectedModel}
                  label="Machine Model"
                  onChange={handleModelChange}
                >
                  {machineConfigs.map((config) => (
                    <MenuItem key={config.id} value={config.model}>
                      Vega {config.model}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Box>
            
            <Box sx={{ padding: 1, width: { xs: '100%', sm: '50%' } }}>
              <TextField
                fullWidth
                label="Machine Price (€)"
                name="price"
                type="number"
                value={formData.price}
                onChange={handleInputChange}
                disabled={isFormDisabled}
                InputProps={{
                  endAdornment: <Typography variant="body2">€</Typography>,
                  inputProps: { step: "0.01" }
                }}
              />
            </Box>
            
            <Box sx={{ padding: 1, width: { xs: '100%', sm: '50%' } }}>
              <TextField
                fullWidth
                label="Maximum Speed"
                name="max_speed"
                type="number"
                value={formData.max_speed}
                onChange={handleInputChange}
                disabled={isFormDisabled}
                InputProps={{
                  endAdornment: <Typography variant="body2">m/min</Typography>,
                  inputProps: { step: "0.1" }
                }}
              />
            </Box>
            
            <Box sx={{ padding: 1, width: { xs: '100%', sm: '50%' } }}>
              <TextField
                fullWidth
                label="Ink Price K (€/kg)"
                name="ink_price_k"
                type="number"
                value={formData.ink_price_k}
                onChange={handleInputChange}
                disabled={isFormDisabled}
                InputProps={{
                  endAdornment: <Typography variant="body2">€/kg</Typography>,
                  inputProps: { step: "0.01" }
                }}
              />
            </Box>
            
            <Box sx={{ padding: 1, width: { xs: '100%', sm: '50%' } }}>
              <TextField
                fullWidth
                label="Ink Price CMY (€/kg)"
                name="ink_price_cmy"
                type="number"
                value={formData.ink_price_cmy}
                onChange={handleInputChange}
                disabled={isFormDisabled}
                InputProps={{
                  endAdornment: <Typography variant="body2">€/kg</Typography>,
                  inputProps: { step: "0.01" }
                }}
              />
            </Box>

            <Box sx={{ padding: 1, width: { xs: '100%', md: '50%' } }}>
              <TextField
                fullWidth
                label="Ink Consumption (g per 1% coverage per m²)"
                name="ink_consumption"
                type="number"
                value={formData.ink_consumption}
                onChange={handleInputChange}
                disabled={isFormDisabled}
                InputProps={{
                  inputProps: { step: "0.001", min: "0" }
                }}
              />
            </Box>

            <Box sx={{ padding: 1, width: { xs: '100%', md: '50%' } }}>
              <TextField
                fullWidth
                label="Assumed Average Coverage (%)"
                name="assumed_coverage"
                type="number"
                value={assumedCoverage}
                onChange={handleCoverageChange}
                disabled={isFormDisabled}
                InputProps={{
                  inputProps: { step: "1", min: 0, max: 400 }
                }}
              />
            </Box>

            <Box sx={{ padding: 1, width: '100%' }}>
              {selectedModel && (
                <Typography variant="caption" display="block" gutterBottom sx={{ mt: 0.5, ml: 1 }}>
                  {pagesPerKgText}
                </Typography>
              )}
            </Box>

            <Box sx={{ padding: 1, width: '100%' }}>
              {selectedModel && (
                <Typography variant="subtitle1" display="block" gutterBottom sx={{ mt: 0, ml: 1, fontWeight: 'medium', textAlign: 'center', color: 'text.secondary' }}>
                  {costPerPageText}
                </Typography>
              )}
            </Box>

            <Box sx={{ padding: 1, width: '100%' }}>
              {selectedModel && (
                <Typography variant="subtitle1" display="block" gutterBottom sx={{ mt: 0, ml: 1, fontWeight: 'medium', textAlign: 'center', color: 'text.secondary' }}>
                  {costPerM2Text}
                </Typography>
              )}
            </Box>
          </Box>
          <Box sx={{ mt: 3, display: 'flex', justifyContent: 'flex-end' }}>
            <Button 
              type="submit" 
              variant="contained" 
              color="primary" 
              disabled={isFormDisabled}
            >
              Save Configuration
            </Button>
          </Box>
        </Box>
      </Paper>
      
      <Snackbar 
        open={snackbar.open} 
        autoHideDuration={6000} 
        onClose={handleCloseSnackbar}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
      >
        <Alert onClose={handleCloseSnackbar} severity={snackbar.severity} sx={{ width: '100%' }}>
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Container>
  );
};

export default MachineConfig;
