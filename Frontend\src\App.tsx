import { useState } from 'react';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import CssBaseline from '@mui/material/CssBaseline';
import Container from '@mui/material/Container';
import Box from '@mui/material/Box';
import Typography from '@mui/material/Typography';
import { BrowserRouter as Router, Routes, Route, Link } from 'react-router-dom';
import { Tabs, Tab } from '@mui/material';
import CalculateIcon from '@mui/icons-material/Calculate';
import ListAltIcon from '@mui/icons-material/ListAlt';
import SettingsIcon from '@mui/icons-material/Settings';

import { Calculator } from './components/Calculator';
import { PaperRollCatalog } from './components/PaperRollCatalog';
import MachineConfig from './components/MachineConfig';
import { Header } from './components/Header';
import { Footer } from './components/Footer';

// Create a theme instance
const theme = createTheme({
  palette: {
    primary: {
      main: '#1976d2',
    },
    secondary: {
      main: '#dc004e',
    },
    background: {
      default: '#f5f5f5',
    },
  },
});

function App() {
  const [tabValue, setTabValue] = useState(0);

  const handleTabChange = (_: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  return (
    <ThemeProvider theme={theme}>
      <CssBaseline />
      <Router>
        <Box
          sx={{
            display: 'flex',
            flexDirection: 'column',
            minHeight: '100vh',
          }}
        >
          <Header />
          <Container component="main" maxWidth="lg" sx={{ my: 4, flex: 1, display: 'flex', flexDirection: 'column', alignItems: 'center', justifyContent: 'center', width: '100%' }}>
            <Box sx={{ width: '100%', maxWidth: '1200px', mb: 3 }}>
              <Typography variant="h4" component="h1" gutterBottom align="center">
                Atexco Vega Calculator
              </Typography>
              <Typography variant="subtitle1" gutterBottom align="center" color="text.secondary">
                Calculate production metrics for Vega digital roll-to-roll printing machines
              </Typography>
            </Box>
            
            <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 3, width: '100%', maxWidth: '1200px' }}>
              <Tabs 
                value={tabValue} 
                onChange={handleTabChange} 
                centered
                variant="fullWidth"
              >
                <Tab 
                  icon={<CalculateIcon />} 
                  label="Calculadora" 
                  component={Link} 
                  to="/" 
                />
                <Tab 
                  icon={<ListAltIcon />} 
                  label="Catálogo de Bobinas" 
                  component={Link} 
                  to="/paper-rolls" 
                />
                <Tab 
                  icon={<SettingsIcon />} 
                  label="Configuración de Máquina" 
                  component={Link} 
                  to="/machine-config" 
                />
              </Tabs>
            </Box>

            <Box sx={{ width: '100%', display: 'flex', justifyContent: 'center' }}>
              <Routes>
                <Route path="/" element={<Calculator />} />
                <Route path="/paper-rolls" element={<PaperRollCatalog />} />
                <Route path="/machine-config" element={<MachineConfig />} />
              </Routes>
            </Box>
          </Container>
        </Box>
        <Footer />
      </Router>
    </ThemeProvider>
  );
}

export default App;
