import json
import os
import random
import base64
from datetime import datetime, timed<PERSON>ta
from typing import Optional, List, Dict, Any
import uuid
from jose import JW<PERSON>rror, jwt

from google.auth.transport.requests import Request
from google.oauth2.credentials import Credentials
from google_auth_oauthlib.flow import Flow
from googleapiclient.discovery import build
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMult<PERSON>art

from models.auth import VerificationCode, TokenData, OAuthTokens

# JWT Configuration
SECRET_KEY = os.getenv("SECRET_KEY", "atexco-calculator-secret-key-change-in-production")
ALGORITHM = "HS256"
ACCESS_TOKEN_EXPIRE_MINUTES = 60 * 24  # 24 hours

class AuthService:
    def __init__(self):
        self.codes_file = "data/verification_codes.json"
        self.tokens_file = "data/oauth_tokens.json"
        self.config_file = "config/email_config.json"
        self._ensure_data_files()
        self.config = self._load_config()
    
    def _ensure_data_files(self):
        """Ensure the data files exist"""
        os.makedirs("data", exist_ok=True)
        os.makedirs("config", exist_ok=True)
        
        if not os.path.exists(self.codes_file):
            with open(self.codes_file, 'w') as f:
                json.dump([], f)
        
        if not os.path.exists(self.tokens_file):
            with open(self.tokens_file, 'w') as f:
                json.dump({}, f)
    
    def _load_config(self) -> Dict[str, Any]:
        """Load email configuration"""
        try:
            with open(self.config_file, 'r') as f:
                return json.load(f)
        except FileNotFoundError:
            raise Exception("Email configuration file not found. Please create config/email_config.json")
    
    def _load_codes(self) -> List[dict]:
        """Load verification codes from file"""
        try:
            with open(self.codes_file, 'r') as f:
                return json.load(f)
        except (FileNotFoundError, json.JSONDecodeError):
            return []
    
    def _save_codes(self, codes: List[dict]):
        """Save verification codes to file"""
        with open(self.codes_file, 'w') as f:
            json.dump(codes, f, default=str, indent=2)
    
    def _load_oauth_tokens(self) -> Dict[str, Any]:
        """Load OAuth tokens from file"""
        try:
            with open(self.tokens_file, 'r') as f:
                return json.load(f)
        except (FileNotFoundError, json.JSONDecodeError):
            return {}
    
    def _save_oauth_tokens(self, tokens: Dict[str, Any]):
        """Save OAuth tokens to file"""
        with open(self.tokens_file, 'w') as f:
            json.dump(tokens, f, default=str, indent=2)
    
    def _generate_code(self) -> str:
        """Generate a 6-digit verification code"""
        code_length = self.config.get("verification", {}).get("code_length", 6)
        min_val = 10**(code_length-1)
        max_val = 10**code_length - 1
        return str(random.randint(min_val, max_val))
    
    def _clean_expired_codes(self):
        """Remove expired codes from storage"""
        codes = self._load_codes()
        now = datetime.now()
        active_codes = []
        
        for code_data in codes:
            expires_at = datetime.fromisoformat(code_data['expires_at'])
            if expires_at > now:
                active_codes.append(code_data)
        
        self._save_codes(active_codes)
    
    def get_oauth_authorization_url(self) -> str:
        """Get OAuth authorization URL for initial setup"""
        oauth_config = self.config["oauth2"]
        
        flow = Flow.from_client_config(
            {
                "web": {
                    "client_id": oauth_config["client_id"],
                    "client_secret": oauth_config["client_secret"],
                    "auth_uri": oauth_config["auth_uri"],
                    "token_uri": oauth_config["token_uri"],
                    "redirect_uris": [oauth_config["redirect_uri"]]
                }
            },
            scopes=oauth_config["scopes"]
        )
        flow.redirect_uri = oauth_config["redirect_uri"]
        
        authorization_url, _ = flow.authorization_url(
            access_type='offline',
            include_granted_scopes='true'
        )
        
        return authorization_url
    
    def handle_oauth_callback(self, authorization_code: str) -> bool:
        """Handle OAuth callback and store tokens"""
        try:
            oauth_config = self.config["oauth2"]
            
            flow = Flow.from_client_config(
                {
                    "web": {
                        "client_id": oauth_config["client_id"],
                        "client_secret": oauth_config["client_secret"],
                        "auth_uri": oauth_config["auth_uri"],
                        "token_uri": oauth_config["token_uri"],
                        "redirect_uris": [oauth_config["redirect_uri"]]
                    }
                },
                scopes=oauth_config["scopes"]
            )
            flow.redirect_uri = oauth_config["redirect_uri"]
            
            flow.fetch_token(code=authorization_code)
            
            credentials = flow.credentials
            
            # Save tokens
            tokens = {
                "access_token": credentials.token,
                "refresh_token": credentials.refresh_token,
                "expires_at": credentials.expiry.isoformat() if credentials.expiry else None,
                "created_at": datetime.now().isoformat()
            }
            
            self._save_oauth_tokens(tokens)
            return True
            
        except Exception as e:
            print(f"Error handling OAuth callback: {e}")
            return False
    
    def _get_gmail_service(self):
        """Get authenticated Gmail service"""
        tokens = self._load_oauth_tokens()
        
        if not tokens:
            raise Exception("No OAuth tokens found. Please complete OAuth setup first.")
        
        oauth_config = self.config["oauth2"]
        
        credentials = Credentials(
            token=tokens["access_token"],
            refresh_token=tokens.get("refresh_token"),
            token_uri=oauth_config["token_uri"],
            client_id=oauth_config["client_id"],
            client_secret=oauth_config["client_secret"],
            scopes=oauth_config["scopes"]
        )
        
        # Refresh token if needed
        if credentials.expired and credentials.refresh_token:
            credentials.refresh(Request())
            
            # Update stored tokens
            tokens.update({
                "access_token": credentials.token,
                "expires_at": credentials.expiry.isoformat() if credentials.expiry else None,
                "updated_at": datetime.now().isoformat()
            })
            self._save_oauth_tokens(tokens)
        
        return build('gmail', 'v1', credentials=credentials)

    def send_verification_code(self, email: str) -> bool:
        """Send verification code to email"""
        try:
            # Clean expired codes first
            self._clean_expired_codes()

            # Check if there's a recent code for this email
            codes = self._load_codes()
            now = datetime.now()

            # Check for existing valid code
            for code_data in codes:
                if code_data['email'] == email and not code_data['used']:
                    expires_at = datetime.fromisoformat(code_data['expires_at'])
                    if expires_at > now:
                        # Don't send new code if there's a valid one
                        return True

            # Generate new code
            code = self._generate_code()
            code_id = str(uuid.uuid4())
            created_at = datetime.now()
            expiry_minutes = self.config.get("verification", {}).get("expiry_minutes", 10)
            expires_at = created_at + timedelta(minutes=expiry_minutes)

            # Remove any existing codes for this email
            codes = [c for c in codes if c['email'] != email]

            # Add new code
            new_code = {
                'id': code_id,
                'email': email,
                'code': code,
                'created_at': created_at.isoformat(),
                'expires_at': expires_at.isoformat(),
                'used': False,
                'attempts': 0
            }
            codes.append(new_code)
            self._save_codes(codes)

            # Send email
            return self._send_email_via_gmail(email, code)

        except Exception as e:
            print(f"Error sending verification code: {e}")
            return False

    def _send_email_via_gmail(self, to_email: str, code: str) -> bool:
        """Send email using Gmail API"""
        try:
            # Check if OAuth tokens are available
            tokens = self._load_oauth_tokens()
            if not tokens or not tokens.get("access_token"):
                # Development mode - just print the code
                print(f"\n{'='*50}")
                print(f"🔐 DEVELOPMENT MODE - EMAIL NOT CONFIGURED")
                print(f"📧 Email: {to_email}")
                print(f"🔢 Verification Code: {code}")
                print(f"{'='*50}\n")
                return True

            service = self._get_gmail_service()
            email_config = self.config["email"]

            # Create message
            message = MIMEMultipart('alternative')
            message['to'] = to_email
            message['from'] = f"{email_config['from_name']} <{email_config['from_email']}>"
            message['subject'] = "Atexco Calculator - Código de Verificación"

            # HTML body
            html_body = f"""
            <html>
            <body style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
                <div style="text-align: center; margin-bottom: 30px;">
                    <h1 style="color: #1976d2; margin-bottom: 10px;">Atexco Calculator</h1>
                    <h2 style="color: #333; margin-top: 0;">Código de Verificación</h2>
                </div>

                <div style="background-color: #f5f5f5; padding: 20px; border-radius: 8px; text-align: center; margin: 20px 0;">
                    <p style="font-size: 16px; color: #666; margin-bottom: 15px;">Tu código de verificación es:</p>
                    <div style="background-color: #1976d2; color: white; font-size: 32px; font-weight: bold; padding: 15px; border-radius: 8px; letter-spacing: 8px; margin: 15px 0;">
                        {code}
                    </div>
                    <p style="font-size: 14px; color: #999; margin-top: 15px;">Este código expirará en 10 minutos.</p>
                </div>

                <div style="text-align: center; margin-top: 30px; padding-top: 20px; border-top: 1px solid #eee;">
                    <p style="font-size: 12px; color: #999;">
                        Si no solicitaste este código, puedes ignorar este correo.
                    </p>
                </div>
            </body>
            </html>
            """

            # Text body (fallback)
            text_body = f"""
            Atexco Calculator - Código de Verificación

            Tu código de verificación es: {code}

            Este código expirará en 10 minutos.

            Si no solicitaste este código, puedes ignorar este correo.
            """

            # Attach both versions
            message.attach(MIMEText(text_body, 'plain'))
            message.attach(MIMEText(html_body, 'html'))

            # Encode message
            raw_message = base64.urlsafe_b64encode(message.as_bytes()).decode('utf-8')

            # Send message
            send_message = service.users().messages().send(
                userId="me",
                body={'raw': raw_message}
            ).execute()

            print(f"Email sent successfully. Message ID: {send_message['id']}")
            return True

        except Exception as e:
            print(f"Error sending email via Gmail: {e}")
            # For development - print the code
            print(f"DEVELOPMENT MODE: Verification code for {to_email}: {code}")
            return True

    def verify_code(self, email: str, code: str) -> Optional[str]:
        """Verify code and return JWT token if valid"""
        try:
            codes = self._load_codes()
            now = datetime.now()
            max_attempts = self.config.get("verification", {}).get("max_attempts", 3)

            for i, code_data in enumerate(codes):
                if code_data['email'] == email and not code_data['used']:
                    expires_at = datetime.fromisoformat(code_data['expires_at'])

                    if expires_at <= now:
                        continue  # Code expired

                    # Increment attempts
                    codes[i]['attempts'] += 1

                    if code_data['code'] == code:
                        # Mark code as used
                        codes[i]['used'] = True
                        self._save_codes(codes)

                        # Generate JWT token
                        return self._create_access_token(email)
                    else:
                        # Check if max attempts reached
                        if codes[i]['attempts'] >= max_attempts:
                            codes[i]['used'] = True  # Invalidate code

                        self._save_codes(codes)
                        return None

            return None

        except Exception as e:
            print(f"Error verifying code: {e}")
            return None

    def _create_access_token(self, email: str) -> str:
        """Create JWT access token"""
        expire = datetime.utcnow() + timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
        to_encode = {"sub": email, "exp": expire}
        encoded_jwt = jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)
        return encoded_jwt

    def verify_token(self, token: str) -> Optional[str]:
        """Verify JWT token and return email if valid"""
        try:
            payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
            email: str = payload.get("sub")
            if email is None:
                return None
            return email
        except JWTError:
            return None
