version: '3.8'

services:
  backend:
    build: ./Backend
    container_name: atexco-backend
    ports:
      - "3006:3006"
    volumes:
      - ./Backend:/app
      - /app/venv
      # Ensure data directory is properly mounted and persisted
      - ./Backend/data:/app/data
      - ./Backend/config:/app/config
    environment:
      - PYTHONPATH=/app
      - PYTHONUNBUFFERED=1
      - HOST=0.0.0.0
      - PORT=3006
      - SECRET_KEY=atexco-calculator-production-secret-key
    restart: unless-stopped
    networks:
      - atexco-network
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:3006/ || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3

  frontend:
    build: ./Frontend
    container_name: atexco-frontend
    ports:
      - "4006:4173"
    # Eliminamos los volúmenes para usar la versión compilada
    # volumes:
    #   - ./Frontend:/app
    #   - /app/node_modules
    depends_on:
      - backend
    environment:
      - VITE_API_URL=http://backend:3006
      - NODE_OPTIONS=--openssl-legacy-provider
    restart: unless-stopped
    networks:
      - atexco-network

networks:
  atexco-network:
    driver: bridge
