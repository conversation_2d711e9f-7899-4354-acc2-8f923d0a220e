version: '3.8'

services:
  backend:
    build: ./Backend
    container_name: atexco-backend
    ports:
      - "3006:3006"
    volumes:
      - ./Backend:/app
      - /app/venv
      # Ensure data directory is properly mounted and persisted
      - ./Backend/data:/app/data
    restart: unless-stopped
    networks:
      - atexco-network

  frontend:
    build: ./Frontend
    container_name: atexco-frontend
    ports:
      - "4006:4173"
    # Eliminamos los volúmenes para usar la versión compilada
    # volumes:
    #   - ./Frontend:/app
    #   - /app/node_modules
    depends_on:
      - backend
    environment:
      - VITE_API_URL=http://backend:3006
      - NODE_OPTIONS=--openssl-legacy-provider
    restart: unless-stopped
    networks:
      - atexco-network

networks:
  atexco-network:
    driver: bridge
