import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import authService from '../services/authService';

interface AuthContextType {
  isAuthenticated: boolean;
  userEmail: string | null;
  loading: boolean;
  login: (email: string) => void;
  logout: () => void;
  checkAuth: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [userEmail, setUserEmail] = useState<string | null>(null);
  const [loading, setLoading] = useState(true);

  const checkAuth = async () => {
    try {
      const token = authService.getToken();
      const email = authService.getEmail();
      
      if (token && email) {
        // Verify token with server
        const isValid = await authService.verifyToken();
        if (isValid) {
          setIsAuthenticated(true);
          setUserEmail(email);
        } else {
          // Token is invalid, clear auth
          authService.clearAuth();
          setIsAuthenticated(false);
          setUserEmail(null);
        }
      } else {
        setIsAuthenticated(false);
        setUserEmail(null);
      }
    } catch (error) {
      console.error('Auth check failed:', error);
      authService.clearAuth();
      setIsAuthenticated(false);
      setUserEmail(null);
    } finally {
      setLoading(false);
    }
  };

  const login = (email: string) => {
    setIsAuthenticated(true);
    setUserEmail(email);
  };

  const logout = () => {
    authService.logout();
    setIsAuthenticated(false);
    setUserEmail(null);
  };

  useEffect(() => {
    checkAuth();
  }, []);

  const value: AuthContextType = {
    isAuthenticated,
    userEmail,
    loading,
    login,
    logout,
    checkAuth
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};
