#!/usr/bin/env python3
"""
Simple server with minimal dependencies for testing
"""

from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from typing import Optional
import random
from datetime import datetime, timedelta
import uvicorn

app = FastAPI(title="Atexco Calculator - Simple Mode")

# Enable CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Simple in-memory storage
verification_codes = {}

# Models
class EmailRequest(BaseModel):
    email: str

class VerificationRequest(BaseModel):
    email: str
    code: str

class AuthResponse(BaseModel):
    success: bool
    message: str
    token: Optional[str] = None

class CalculationInput(BaseModel):
    machine_model: str
    page_width: float
    page_height: float
    pages: int
    is_duplex: bool
    coverage: float
    is_color: bool
    copies: int
    paper_roll_id: Optional[str] = None

# Routes
@app.get("/")
async def root():
    return {"message": "Atexco Calculator - Simple Mode"}

@app.post("/auth/send-code")
async def send_code(request: EmailRequest):
    try:
        code = str(random.randint(100000, 999999))
        verification_codes[request.email] = {
            'code': code,
            'expires_at': datetime.now() + timedelta(minutes=10),
            'attempts': 0
        }
        
        print(f"\n{'='*50}")
        print(f"🔐 CÓDIGO DE VERIFICACIÓN")
        print(f"📧 Email: {request.email}")
        print(f"🔢 Código: {code}")
        print(f"{'='*50}\n")
        
        return AuthResponse(success=True, message="Verification code sent successfully")
    except Exception as e:
        print(f"Error sending code: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/auth/verify-code")
async def verify_code(request: VerificationRequest):
    try:
        email = request.email
        code = request.code
        
        if email not in verification_codes:
            raise HTTPException(status_code=400, detail="No verification code found")
        
        stored_data = verification_codes[email]
        
        if datetime.now() > stored_data['expires_at']:
            del verification_codes[email]
            raise HTTPException(status_code=400, detail="Code expired")
        
        if stored_data['attempts'] >= 3:
            del verification_codes[email]
            raise HTTPException(status_code=400, detail="Too many attempts")
        
        if stored_data['code'] != code:
            verification_codes[email]['attempts'] += 1
            raise HTTPException(status_code=400, detail="Invalid code")
        
        del verification_codes[email]
        
        # Simple token
        import base64
        token = base64.b64encode(f"{email}:{datetime.now().isoformat()}".encode()).decode()
        
        return AuthResponse(success=True, message="Code verified successfully", token=token)
        
    except HTTPException:
        raise
    except Exception as e:
        print(f"Error verifying code: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/calculate")
async def calculate(data: CalculationInput):
    try:
        print(f"Calculation request: {data.dict()}")
        
        # Simple calculation logic
        page_area_m2 = (data.page_width / 1000) * (data.page_height / 1000)
        linear_meters_per_copy = (data.page_height / 1000) * data.pages
        total_linear_meters = linear_meters_per_copy * data.copies
        
        # Mock values for other fields
        response = {
            "linear_meters_per_copy": round(linear_meters_per_copy, 2),
            "total_linear_meters": round(total_linear_meters, 2),
            "products_per_width": 2,
            "speed_meters_per_minute": 100.0,
            "print_time_minutes_per_copy": round(linear_meters_per_copy / 100, 3),
            "total_print_time_minutes": round(total_linear_meters / 100, 3),
            "copies": data.copies,
            "total_pages": data.pages * data.copies,
            "paper_roll_id": data.paper_roll_id,
            "paper_roll_supplier": "Test Supplier",
            "paper_roll_quality": "Test Quality",
            "paper_roll_width": 44.0,
            "paper_roll_grammage": 90,
            "paper_cost": round(total_linear_meters * 0.35, 2),
            "ink_cost_per_copy": round(page_area_m2 * data.coverage * 0.001, 3),
            "total_ink_cost": round(page_area_m2 * data.coverage * 0.001 * data.copies, 2),
            "ink_cost_per_page": round(page_area_m2 * data.coverage * 0.0001, 4),
            "ink_grams_per_copy": round(page_area_m2 * data.coverage * 0.1, 2),
            "total_ink_grams": round(page_area_m2 * data.coverage * 0.1 * data.copies, 2)
        }
        
        print(f"Calculation response: {response}")
        return response
        
    except Exception as e:
        print(f"Error in calculation: {e}")
        import traceback
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/paper-rolls")
async def get_paper_rolls():
    return [
        {
            "id": "1",
            "supplier": "BURGO",
            "quality": "UNO PRIME SATIN",
            "width": 44.0,
            "grammage": 90,
            "weight": 500.0,
            "price_per_ton": 840.0
        },
        {
            "id": "2",
            "supplier": "SAPPI",
            "quality": "MAGNO WEB SILK",
            "width": 44.0,
            "grammage": 90,
            "weight": 500.0,
            "price_per_ton": 840.0
        }
    ]

if __name__ == "__main__":
    print("🚀 Starting Simple Atexco Calculator Server...")
    print("📍 Server: http://localhost:3006")
    print("📖 Docs: http://localhost:3006/docs")
    print("-" * 50)
    
    uvicorn.run(
        "simple_server:app",
        host="0.0.0.0",
        port=3006,
        reload=True,
        log_level="info"
    )
