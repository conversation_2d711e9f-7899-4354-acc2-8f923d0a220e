#!/usr/bin/env python3
"""
Arreglar tokens OAuth con todos los campos necesarios
"""

import json
import os
from datetime import datetime
from services.auth_service import AuthService

def check_current_tokens():
    """Verificar estado actual de los tokens"""
    print("🔍 Verificando tokens OAuth actuales...")
    
    try:
        with open('data/oauth_tokens.json', 'r') as f:
            tokens = json.load(f)
        
        print("📋 Campos actuales en oauth_tokens.json:")
        for key, value in tokens.items():
            if key == 'access_token':
                print(f"   {key}: {value[:20]}... (truncado)")
            elif key == 'refresh_token':
                print(f"   {key}: {'✅ Presente' if value else '❌ Ausente'}")
            else:
                print(f"   {key}: {value}")
        
        # Verificar campos necesarios
        required_fields = ['access_token', 'refresh_token', 'token_uri', 'client_id', 'client_secret']
        missing_fields = []
        
        for field in required_fields:
            if field not in tokens or not tokens[field]:
                missing_fields.append(field)
        
        if missing_fields:
            print(f"\n❌ Campos faltantes: {', '.join(missing_fields)}")
            return False
        else:
            print("\n✅ Todos los campos necesarios están presentes")
            return True
            
    except Exception as e:
        print(f"❌ Error leyendo tokens: {e}")
        return False

def fix_oauth_tokens():
    """Arreglar tokens OAuth agregando campos faltantes"""
    print("\n🔧 Arreglando tokens OAuth...")
    
    try:
        # Cargar configuración
        with open('config/email_config.json', 'r') as f:
            config = json.load(f)
        
        oauth_config = config['oauth2']
        
        # Cargar tokens actuales
        with open('data/oauth_tokens.json', 'r') as f:
            tokens = json.load(f)
        
        # Agregar campos faltantes desde la configuración
        tokens.update({
            'token_uri': oauth_config['token_uri'],
            'client_id': oauth_config['client_id'],
            'client_secret': oauth_config['client_secret']
        })
        
        # Si no hay refresh_token, necesitamos reautorizar
        if not tokens.get('refresh_token'):
            print("❌ No hay refresh_token - necesitas reautorizar")
            return False
        
        # Guardar tokens actualizados
        with open('data/oauth_tokens.json', 'w') as f:
            json.dump(tokens, f, indent=2, default=str)
        
        print("✅ Tokens OAuth actualizados con campos faltantes")
        return True
        
    except Exception as e:
        print(f"❌ Error arreglando tokens: {e}")
        return False

def test_fixed_tokens():
    """Probar tokens arreglados"""
    print("\n🧪 Probando tokens arreglados...")
    
    try:
        auth_service = AuthService()
        
        # Intentar obtener servicio Gmail
        gmail_service = auth_service._get_gmail_service()
        print("✅ Servicio Gmail obtenido correctamente")
        
        # Probar envío de email
        test_email = input("\n¿Quieres probar enviando un email? (email o Enter para omitir): ").strip()
        
        if test_email:
            print(f"\n📧 Probando envío a {test_email}...")
            success = auth_service.send_verification_code(test_email)
            
            if success:
                print("✅ ¡Email enviado exitosamente!")
                return True
            else:
                print("❌ Error enviando email")
                return False
        else:
            print("✅ Tokens funcionan correctamente")
            return True
            
    except Exception as e:
        print(f"❌ Error probando tokens: {e}")
        import traceback
        traceback.print_exc()
        return False

def regenerate_oauth_completely():
    """Regenerar OAuth completamente"""
    print("\n🔄 Regenerando OAuth completamente...")
    print("Esto requiere reautorización con Google...")
    
    response = input("¿Continuar? (y/N): ").strip().lower()
    if response not in ['y', 'yes', 'sí', 'si']:
        return False
    
    try:
        # Ejecutar script de OAuth
        import subprocess
        result = subprocess.run(['python', 'quick_oauth_setup.py'], 
                              capture_output=False, text=True)
        
        return result.returncode == 0
        
    except Exception as e:
        print(f"❌ Error regenerando OAuth: {e}")
        return False

def main():
    print("🔧 Reparador de Tokens OAuth - Atexco Calculator")
    print("=" * 50)
    
    # Verificar tokens actuales
    tokens_ok = check_current_tokens()
    
    if tokens_ok:
        print("\n✅ Los tokens parecen estar completos")
        
        # Probar si funcionan
        if test_fixed_tokens():
            print("\n🎉 ¡Tokens OAuth funcionando correctamente!")
            return True
        else:
            print("\n⚠️  Los tokens están completos pero no funcionan")
    
    # Intentar arreglar tokens
    print("\n🔧 Intentando arreglar tokens...")
    
    if fix_oauth_tokens():
        print("✅ Tokens arreglados")
        
        # Probar tokens arreglados
        if test_fixed_tokens():
            print("\n🎉 ¡Tokens OAuth reparados y funcionando!")
            return True
        else:
            print("\n❌ Los tokens siguen sin funcionar")
    
    # Si nada funciona, regenerar completamente
    print("\n🔄 Los tokens necesitan regenerarse completamente")
    
    if regenerate_oauth_completely():
        print("\n🎉 ¡OAuth regenerado exitosamente!")
        return True
    else:
        print("\n❌ No se pudo regenerar OAuth")
        return False

if __name__ == "__main__":
    success = main()
    
    if success:
        print("\n✅ OAuth está funcionando correctamente")
        print("Puedes usar el sistema de autenticación con envío real de emails")
    else:
        print("\n❌ OAuth sigue sin funcionar")
        print("Ejecuta manualmente: python quick_oauth_setup.py")
    
    input("\nPresiona Enter para salir...")
