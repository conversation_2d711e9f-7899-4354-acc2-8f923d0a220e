import axios from 'axios';
import { MachineConfig } from '../models/MachineConfig';

import { API_URL } from '../config';

export const getMachineConfigs = async (): Promise<MachineConfig[]> => {
  try {
    const response = await axios.get(`${API_URL}/machine-configs/`);
    return response.data;
  } catch (error) {
    console.error('Error fetching machine configurations:', error);
    throw error;
  }
};

export const getMachineConfigById = async (id: string): Promise<MachineConfig> => {
  try {
    const response = await axios.get(`${API_URL}/machine-configs/${id}`);
    return response.data;
  } catch (error) {
    console.error(`Error fetching machine configuration with ID ${id}:`, error);
    throw error;
  }
};

export const getMachineConfigByModel = async (model: string): Promise<MachineConfig> => {
  try {
    const response = await axios.get(`${API_URL}/machine-configs/model/${model}`);
    return response.data;
  } catch (error) {
    console.error(`Error fetching machine configuration for model ${model}:`, error);
    throw error;
  }
};

export const createMachineConfig = async (machineConfig: Omit<MachineConfig, 'id'>): Promise<MachineConfig> => {
  try {
    const response = await axios.post(`${API_URL}/machine-configs/`, machineConfig);
    return response.data;
  } catch (error) {
    console.error('Error creating machine configuration:', error);
    throw error;
  }
};

export const updateMachineConfig = async (id: string, machineConfig: Omit<MachineConfig, 'id'>): Promise<MachineConfig> => {
  try {
    const response = await axios.put(`${API_URL}/machine-configs/${id}`, machineConfig);
    return response.data;
  } catch (error) {
    console.error(`Error updating machine configuration with ID ${id}:`, error);
    throw error;
  }
};

export const deleteMachineConfig = async (id: string): Promise<void> => {
  try {
    await axios.delete(`${API_URL}/machine-configs/${id}`);
  } catch (error) {
    console.error(`Error deleting machine configuration with ID ${id}:`, error);
    throw error;
  }
};
