from pydantic import BaseModel, Field

class MachineConfig(BaseModel):
    id: str
    model: str = Field(..., description="Modelo de la máquina (440, 660, 880)")
    price: float = Field(..., description="Precio de la máquina en euros")
    max_speed: float = Field(..., description="Velocidad máxima de la máquina en metros/minuto")
    ink_price_k: float = Field(..., description="Precio de la tinta K (negra) en euros/kg")
    ink_price_cmy: float = Field(..., description="Precio de la tinta CMY (color) en euros/kg")
    ink_consumption: float = Field(..., description="Consumo de tinta en gramos por cada 1% de cobertura por m²")
