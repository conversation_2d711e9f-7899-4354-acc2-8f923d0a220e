from fastapi import APIRouter, HTTPException
from typing import List

from models.machine_config import MachineConfig
from services.machine_config_service import MachineConfigService

router = APIRouter(
    prefix="/machine-configs",
    tags=["machine-configs"],
    responses={404: {"description": "Not found"}},
)

machine_config_service = MachineConfigService()

@router.get("/", response_model=List[MachineConfig])
async def get_all_machine_configs():
    return machine_config_service.get_all()

@router.get("/{id}", response_model=MachineConfig)
async def get_machine_config(id: str):
    machine_config = machine_config_service.get_by_id(id)
    if machine_config is None:
        raise HTTPException(status_code=404, detail="Machine configuration not found")
    return machine_config

@router.get("/model/{model}", response_model=MachineConfig)
async def get_machine_config_by_model(model: str):
    machine_config = machine_config_service.get_by_model(model)
    if machine_config is None:
        raise HTTPException(status_code=404, detail="Machine configuration not found")
    return machine_config

@router.post("/", response_model=MachineConfig)
async def create_machine_config(machine_config: MachineConfig):
    return machine_config_service.create(machine_config)

@router.put("/{id}", response_model=MachineConfig)
async def update_machine_config(id: str, machine_config: MachineConfig):
    updated_config = machine_config_service.update(id, machine_config)
    if updated_config is None:
        raise HTTPException(status_code=404, detail="Machine configuration not found")
    return updated_config

@router.delete("/{id}")
async def delete_machine_config(id: str):
    success = machine_config_service.delete(id)
    if not success:
        raise HTTPException(status_code=404, detail="Machine configuration not found")
    return {"status": "success"}
