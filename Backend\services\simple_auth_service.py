import json
import os
import random
from datetime import datetime, timedelta
from typing import Optional, List, Dict, Any
import uuid

# Simple JWT implementation without external dependencies
import base64
import hmac
import hashlib

# JWT Configuration
SECRET_KEY = os.getenv("SECRET_KEY", "atexco-calculator-secret-key-change-in-production")
ACCESS_TOKEN_EXPIRE_MINUTES = 60 * 24  # 24 hours

class SimpleAuthService:
    def __init__(self):
        self.codes_file = "data/verification_codes.json"
        self.config_file = "config/email_config.json"
        self._ensure_data_files()
        self.config = self._load_config()
    
    def _ensure_data_files(self):
        """Ensure the data files exist"""
        os.makedirs("data", exist_ok=True)
        os.makedirs("config", exist_ok=True)
        
        if not os.path.exists(self.codes_file):
            with open(self.codes_file, 'w') as f:
                json.dump([], f)
    
    def _load_config(self) -> Dict[str, Any]:
        """Load email configuration"""
        try:
            with open(self.config_file, 'r') as f:
                return json.load(f)
        except FileNotFoundError:
            # Return default config if file doesn't exist
            return {
                "verification": {
                    "code_length": 6,
                    "expiry_minutes": 10,
                    "max_attempts": 3
                }
            }
    
    def _load_codes(self) -> List[dict]:
        """Load verification codes from file"""
        try:
            with open(self.codes_file, 'r') as f:
                return json.load(f)
        except (FileNotFoundError, json.JSONDecodeError):
            return []
    
    def _save_codes(self, codes: List[dict]):
        """Save verification codes to file"""
        with open(self.codes_file, 'w') as f:
            json.dump(codes, f, default=str, indent=2)
    
    def _generate_code(self) -> str:
        """Generate a 6-digit verification code"""
        code_length = self.config.get("verification", {}).get("code_length", 6)
        min_val = 10**(code_length-1)
        max_val = 10**code_length - 1
        return str(random.randint(min_val, max_val))
    
    def _clean_expired_codes(self):
        """Remove expired codes from storage"""
        codes = self._load_codes()
        now = datetime.now()
        active_codes = []
        
        for code_data in codes:
            try:
                expires_at = datetime.fromisoformat(code_data['expires_at'])
                if expires_at > now:
                    active_codes.append(code_data)
            except (ValueError, KeyError):
                # Skip invalid entries
                continue
        
        self._save_codes(active_codes)
    
    def send_verification_code(self, email: str) -> bool:
        """Send verification code to email (development mode)"""
        try:
            # Clean expired codes first
            self._clean_expired_codes()
            
            # Check if there's a recent code for this email
            codes = self._load_codes()
            now = datetime.now()
            
            # Check for existing valid code
            for code_data in codes:
                if code_data['email'] == email and not code_data['used']:
                    try:
                        expires_at = datetime.fromisoformat(code_data['expires_at'])
                        if expires_at > now:
                            # Don't send new code if there's a valid one
                            print(f"Using existing code for {email}: {code_data['code']}")
                            return True
                    except (ValueError, KeyError):
                        continue
            
            # Generate new code
            code = self._generate_code()
            code_id = str(uuid.uuid4())
            created_at = datetime.now()
            expiry_minutes = self.config.get("verification", {}).get("expiry_minutes", 10)
            expires_at = created_at + timedelta(minutes=expiry_minutes)
            
            # Remove any existing codes for this email
            codes = [c for c in codes if c.get('email') != email]
            
            # Add new code
            new_code = {
                'id': code_id,
                'email': email,
                'code': code,
                'created_at': created_at.isoformat(),
                'expires_at': expires_at.isoformat(),
                'used': False,
                'attempts': 0
            }
            codes.append(new_code)
            self._save_codes(codes)
            
            # Development mode - print the code
            print(f"\n{'='*60}")
            print(f"🔐 MODO DESARROLLO - CÓDIGO DE VERIFICACIÓN")
            print(f"📧 Email: {email}")
            print(f"🔢 Código: {code}")
            print(f"⏰ Expira en: {expiry_minutes} minutos")
            print(f"{'='*60}\n")
            
            return True
            
        except Exception as e:
            print(f"Error sending verification code: {e}")
            return False
    
    def verify_code(self, email: str, code: str) -> Optional[str]:
        """Verify code and return JWT token if valid"""
        try:
            codes = self._load_codes()
            now = datetime.now()
            max_attempts = self.config.get("verification", {}).get("max_attempts", 3)
            
            for i, code_data in enumerate(codes):
                if code_data.get('email') == email and not code_data.get('used', True):
                    try:
                        expires_at = datetime.fromisoformat(code_data['expires_at'])
                    except (ValueError, KeyError):
                        continue
                    
                    if expires_at <= now:
                        continue  # Code expired
                    
                    # Increment attempts
                    codes[i]['attempts'] = codes[i].get('attempts', 0) + 1
                    
                    if code_data.get('code') == code:
                        # Mark code as used
                        codes[i]['used'] = True
                        self._save_codes(codes)
                        
                        # Generate JWT token
                        return self._create_access_token(email)
                    else:
                        # Check if max attempts reached
                        if codes[i]['attempts'] >= max_attempts:
                            codes[i]['used'] = True  # Invalidate code
                        
                        self._save_codes(codes)
                        return None
            
            return None
            
        except Exception as e:
            print(f"Error verifying code: {e}")
            return None
    
    def _create_access_token(self, email: str) -> str:
        """Create simple JWT access token"""
        try:
            expire = datetime.now() + timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
            
            # Create payload
            payload = {
                "sub": email,
                "exp": int(expire.timestamp()),
                "iat": int(datetime.now().timestamp())
            }
            
            # Simple JWT implementation
            header = {"alg": "HS256", "typ": "JWT"}
            
            # Encode header and payload
            header_encoded = base64.urlsafe_b64encode(
                json.dumps(header, separators=(',', ':')).encode()
            ).decode().rstrip('=')
            
            payload_encoded = base64.urlsafe_b64encode(
                json.dumps(payload, separators=(',', ':')).encode()
            ).decode().rstrip('=')
            
            # Create signature
            message = f"{header_encoded}.{payload_encoded}"
            signature = hmac.new(
                SECRET_KEY.encode(),
                message.encode(),
                hashlib.sha256
            ).digest()
            
            signature_encoded = base64.urlsafe_b64encode(signature).decode().rstrip('=')
            
            return f"{message}.{signature_encoded}"
            
        except Exception as e:
            print(f"Error creating token: {e}")
            raise
    
    def verify_token(self, token: str) -> Optional[str]:
        """Verify JWT token and return email if valid"""
        try:
            # Split token
            parts = token.split('.')
            if len(parts) != 3:
                return None
            
            header_encoded, payload_encoded, signature_encoded = parts
            
            # Verify signature
            message = f"{header_encoded}.{payload_encoded}"
            expected_signature = hmac.new(
                SECRET_KEY.encode(),
                message.encode(),
                hashlib.sha256
            ).digest()
            
            # Pad base64 if needed
            signature_encoded += '=' * (4 - len(signature_encoded) % 4)
            received_signature = base64.urlsafe_b64decode(signature_encoded)
            
            if not hmac.compare_digest(expected_signature, received_signature):
                return None
            
            # Decode payload
            payload_encoded += '=' * (4 - len(payload_encoded) % 4)
            payload = json.loads(base64.urlsafe_b64decode(payload_encoded))
            
            # Check expiration
            if payload.get('exp', 0) < datetime.now().timestamp():
                return None
            
            return payload.get('sub')
            
        except Exception as e:
            print(f"Error verifying token: {e}")
            return None
