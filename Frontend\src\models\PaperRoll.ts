export interface PaperRoll {
  id: string;
  supplier: string;
  quality: string;
  width: number;
  grammage: number;
  weight: number;
  linear_meters: number;
  price_per_m2: number;
  price_per_ton: number;
  created_at: string;
  updated_at: string | null;
}

export interface PaperRollCreate {
  supplier: string;
  quality: string;
  width: number;
  grammage: number;
  weight: number;
  price_per_ton?: number;
}

export interface PaperRollUpdate {
  supplier?: string;
  quality?: string;
  width?: number;
  grammage?: number;
  weight?: number;
  price_per_ton?: number;
}
