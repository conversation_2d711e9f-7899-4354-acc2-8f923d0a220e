from fastapi import APIRouter, HTTPException
from pydantic import BaseModel
import json
import os
import random
from datetime import datetime, timedelta
import uuid

router = APIRouter(
    prefix="/test-auth",
    tags=["test-authentication"],
)

class EmailRequest(BaseModel):
    email: str

class VerificationRequest(BaseModel):
    email: str
    code: str

class AuthResponse(BaseModel):
    success: bool
    message: str
    token: str = None

# Simple in-memory storage for testing
verification_codes = {}

@router.post("/send-code", response_model=AuthResponse)
async def send_verification_code(request: EmailRequest):
    """Send verification code to email (test version)"""
    try:
        # Generate 6-digit code
        code = str(random.randint(100000, 999999))
        
        # Store code with expiration
        verification_codes[request.email] = {
            'code': code,
            'expires_at': datetime.now() + timedelta(minutes=10),
            'attempts': 0
        }
        
        # Print code for development
        print(f"\n{'='*50}")
        print(f"🔐 TEST AUTH - CÓDIGO DE VERIFICACIÓN")
        print(f"📧 Email: {request.email}")
        print(f"🔢 Código: {code}")
        print(f"{'='*50}\n")
        
        return AuthResponse(
            success=True,
            message="Verification code sent successfully"
        )
        
    except Exception as e:
        print(f"Error in test send_code: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/verify-code", response_model=AuthResponse)
async def verify_code(request: VerificationRequest):
    """Verify code and return token (test version)"""
    try:
        email = request.email
        code = request.code
        
        print(f"Verifying code {code} for email {email}")
        
        # Check if email has a code
        if email not in verification_codes:
            print(f"No code found for email: {email}")
            raise HTTPException(status_code=400, detail="No verification code found for this email")
        
        stored_data = verification_codes[email]
        
        # Check expiration
        if datetime.now() > stored_data['expires_at']:
            print(f"Code expired for email: {email}")
            del verification_codes[email]
            raise HTTPException(status_code=400, detail="Verification code has expired")
        
        # Check attempts
        if stored_data['attempts'] >= 3:
            print(f"Too many attempts for email: {email}")
            del verification_codes[email]
            raise HTTPException(status_code=400, detail="Too many attempts")
        
        # Verify code
        if stored_data['code'] != code:
            print(f"Invalid code for email: {email}")
            verification_codes[email]['attempts'] += 1
            raise HTTPException(status_code=400, detail="Invalid verification code")
        
        # Success - remove code and generate token
        del verification_codes[email]
        
        # Simple token (just base64 encoded email for testing)
        import base64
        token = base64.b64encode(f"{email}:{datetime.now().isoformat()}".encode()).decode()
        
        print(f"Code verified successfully for: {email}")
        
        return AuthResponse(
            success=True,
            message="Code verified successfully",
            token=token
        )
        
    except HTTPException:
        raise
    except Exception as e:
        print(f"Error in test verify_code: {e}")
        import traceback
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/status")
async def get_status():
    """Get auth system status"""
    return {
        "status": "ok",
        "active_codes": len(verification_codes),
        "message": "Test auth system is running"
    }
