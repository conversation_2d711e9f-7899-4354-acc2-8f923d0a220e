#!/usr/bin/env python3
"""
Servidor temporal para capturar el callback de OAuth y mostrar el código
"""

from fastapi import FastAPI, Request
from fastapi.responses import HTMLResponse
import uvicorn
import webbrowser
import threading
import time
from services.auth_service import AuthService

app = FastAPI()

# Variable global para almacenar el código
authorization_code = None
server_should_stop = False

@app.get("/auth/oauth/callback")
async def oauth_callback(request: Request):
    global authorization_code, server_should_stop
    
    # Obtener el código de la query string
    code = request.query_params.get('code')
    error = request.query_params.get('error')
    
    if error:
        return HTMLResponse(f"""
        <html>
        <head><title>Error de OAuth</title></head>
        <body style="font-family: Arial, sans-serif; padding: 40px; text-align: center;">
            <h1 style="color: red;">❌ Error de OAuth</h1>
            <p>Error: {error}</p>
            <p>Descripción: {request.query_params.get('error_description', 'Error desconocido')}</p>
            <p>Puedes cerrar esta ventana.</p>
        </body>
        </html>
        """)
    
    if code:
        authorization_code = code
        server_should_stop = True
        
        return HTMLResponse(f"""
        <html>
        <head><title>OAuth Exitoso</title></head>
        <body style="font-family: Arial, sans-serif; padding: 40px; text-align: center;">
            <h1 style="color: green;">✅ ¡Autorización Exitosa!</h1>
            <p>Código de autorización recibido correctamente.</p>
            <p>El código se está procesando automáticamente...</p>
            <p>Puedes cerrar esta ventana.</p>
            <script>
                setTimeout(function() {{
                    window.close();
                }}, 3000);
            </script>
        </body>
        </html>
        """)
    
    return HTMLResponse("""
    <html>
    <head><title>Callback OAuth</title></head>
    <body style="font-family: Arial, sans-serif; padding: 40px; text-align: center;">
        <h1>❌ No se recibió código de autorización</h1>
        <p>Puedes cerrar esta ventana e intentar de nuevo.</p>
    </body>
    </html>
    """)

def run_server():
    """Ejecutar el servidor en un hilo separado"""
    uvicorn.run(app, host="localhost", port=3006, log_level="error")

def oauth_setup_with_server():
    """Configurar OAuth con servidor local"""
    global authorization_code, server_should_stop
    
    print("🚀 Iniciando servidor temporal para OAuth...")
    
    # Iniciar servidor en hilo separado
    server_thread = threading.Thread(target=run_server, daemon=True)
    server_thread.start()
    
    # Esperar un poco para que el servidor inicie
    time.sleep(2)
    
    try:
        # Crear servicio de auth
        auth_service = AuthService()
        
        # Obtener URL de autorización
        auth_url = auth_service.get_oauth_authorization_url()
        
        print("🔗 Abriendo navegador para autorización OAuth...")
        print(f"URL: {auth_url}")
        
        # Abrir navegador
        webbrowser.open(auth_url)
        
        print("\n📋 Instrucciones:")
        print("1. Se abrirá tu navegador automáticamente")
        print("2. Inicia sesió<NAME_EMAIL>")
        print("3. Autoriza la aplicación")
        print("4. Serás redirigido automáticamente")
        print("5. El código se procesará automáticamente")
        
        print("\n⏳ Esperando autorización...")
        
        # Esperar hasta 120 segundos por el código
        timeout = 120
        start_time = time.time()
        
        while not authorization_code and not server_should_stop:
            if time.time() - start_time > timeout:
                print("❌ Timeout: No se recibió autorización en 2 minutos")
                return False
            time.sleep(1)
        
        if authorization_code:
            print(f"✅ Código recibido: {authorization_code[:20]}...")
            
            # Procesar el código
            print("🔄 Procesando código de autorización...")
            success = auth_service.handle_oauth_callback(authorization_code)
            
            if success:
                print("✅ ¡OAuth configurado exitosamente!")
                
                # Probar envío de email
                test_email = input("\n¿Quieres probar enviando un email? Ingresa un email (o Enter para omitir): ").strip()
                if test_email:
                    print(f"📧 Enviando email de prueba a {test_email}...")
                    try:
                        result = auth_service.send_verification_code(test_email)
                        if result:
                            print("✅ ¡Email enviado exitosamente!")
                            print("🔍 Revisa la bandeja de entrada (y spam)")
                        else:
                            print("❌ Error al enviar email")
                    except Exception as e:
                        print(f"❌ Error: {e}")
                
                return True
            else:
                print("❌ Error al procesar el código")
                return False
        else:
            print("❌ No se recibió código de autorización")
            return False
            
    except Exception as e:
        print(f"❌ Error durante la configuración: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("=== Configuración OAuth 2.0 con Servidor Local ===\n")
    
    success = oauth_setup_with_server()
    
    if success:
        print("\n🎉 ¡Configuración completada exitosamente!")
        print("El sistema de autenticación OAuth está listo para usar.")
    else:
        print("\n❌ La configuración no se completó.")
        print("Verifica que hayas agregado http://localhost:3006/auth/oauth/callback")
        print("como URI de redirección en Google Cloud Console.")
    
    print("\nPresiona Enter para salir...")
    input()
