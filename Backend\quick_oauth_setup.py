#!/usr/bin/env python3
"""
Configuración rápida de OAuth - genera nueva URL y procesa código inmediatamente
"""

from services.auth_service import AuthService
import webbrowser

def quick_oauth_setup():
    print("=== Configuración Rápida de OAuth ===\n")
    
    try:
        # Crear servicio de auth
        auth_service = AuthService()
        
        # Generar nueva URL de autorización
        auth_url = auth_service.get_oauth_authorization_url()
        
        print("🔗 Nueva URL de autorización:")
        print(f"   {auth_url}")
        print()
        
        # Intentar abrir en navegador
        try:
            webbrowser.open(auth_url)
            print("✅ URL abierta en el navegador")
        except:
            print("❌ No se pudo abrir automáticamente")
        
        print("\n📋 INSTRUCCIONES RÁPIDAS:")
        print("1. Se abrió (o copia) la URL en tu navegador")
        print("2. Inicia sesió<NAME_EMAIL>")
        print("3. Autoriza la aplicación")
        print("4. Serás redirigido a una página con error 404 - ¡ES NORMAL!")
        print("5. En la barra de direcciones, busca 'code=' en la URL")
        print("6. Copia SOLO la parte después de 'code=' y antes del siguiente '&'")
        print()
        
        print("Ejemplo:")
        print("URL: http://localhost:3006/auth/oauth/callback?code=4/0AUJR-x7M...&scope=...")
        print("Copia: 4/0AUJR-x7M...")
        print()
        
        # Solicitar código
        print("⏰ IMPORTANTE: Hazlo rápido, el código expira en pocos minutos")
        print("-" * 60)
        
        code = input("Pega el nuevo código de autorización aquí: ").strip()
        
        if not code:
            print("❌ No se proporcionó código")
            return False
        
        # Limpiar el código
        if '&' in code:
            code = code.split('&')[0]
        if code.startswith('code='):
            code = code[5:]
        
        print(f"\n🔄 Procesando código: {code[:30]}...")
        
        # Procesar inmediatamente
        success = auth_service.handle_oauth_callback(code)
        
        if success:
            print("✅ ¡OAuth configurado exitosamente!")
            print("🎉 Gmail OAuth 2.0 <NAME_EMAIL>")
            
            # Probar inmediatamente
            print("\n" + "="*50)
            print("🧪 PRUEBA INMEDIATA")
            
            test_email = input("Ingresa un email para probar el envío real: ").strip()
            
            if test_email:
                print(f"\n📧 <NAME_EMAIL> a {test_email}...")
                
                try:
                    result = auth_service.send_verification_code(test_email)
                    if result:
                        print("✅ ¡EMAIL ENVIADO EXITOSAMENTE!")
                        print("📬 Revisa la bandeja de entrada y spam")
                        print("📧 Remitente: <EMAIL>")
                        
                        # Mostrar código para verificación
                        import json
                        try:
                            with open('data/verification_codes.json', 'r') as f:
                                codes = json.load(f)
                            
                            for code_data in reversed(codes):
                                if code_data['email'] == test_email:
                                    print(f"🔢 Código enviado: {code_data['code']}")
                                    break
                        except:
                            pass
                        
                        return True
                    else:
                        print("❌ Error al enviar email")
                        return False
                except Exception as e:
                    print(f"❌ Error: {e}")
                    return False
            else:
                print("✅ OAuth configurado, pero no se probó el envío")
                return True
        else:
            print("❌ Error al procesar el código")
            return False
            
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = quick_oauth_setup()
    
    if success:
        print("\n🎉 ¡ÉXITO TOTAL!")
        print("El sistema OAuth está funcionando perfectamente.")
        print("Puedes usar la autenticación con envío real de emails.")
    else:
        print("\n❌ Algo salió mal.")
        print("Puedes intentar de nuevo - los códigos expiran rápido.")
    
    input("\nPresiona Enter para salir...")
