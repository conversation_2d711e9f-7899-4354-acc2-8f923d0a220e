import json
import os
import uuid
from typing import List, Optional

from models.machine_config import MachineConfig

class MachineConfigService:
    def __init__(self):
        # Use absolute path to ensure file is found regardless of execution directory
        current_dir = os.path.dirname(os.path.abspath(__file__))
        self.file_path = os.path.join(os.path.dirname(current_dir), "data", "machine_configs.json")
        self._load_data()

    def _load_data(self):
        if os.path.exists(self.file_path):
            with open(self.file_path, "r", encoding="utf-8") as f:
                data = json.load(f)
                self.machine_configs = [MachineConfig(**item) for item in data]
        else:
            self.machine_configs = []
            self._seed_initial_data()
            self._save_data()

    def _save_data(self):
        with open(self.file_path, "w", encoding="utf-8") as f:
            json.dump([config.dict() for config in self.machine_configs], f, indent=2, ensure_ascii=False)

    def get_all(self) -> List[MachineConfig]:
        return self.machine_configs

    def get_by_id(self, id: str) -> Optional[MachineConfig]:
        for config in self.machine_configs:
            if config.id == id:
                return config
        return None

    def get_by_model(self, model: str) -> Optional[MachineConfig]:
        for config in self.machine_configs:
            if config.model == model:
                return config
        return None

    def create(self, machine_config: MachineConfig) -> MachineConfig:
        machine_config.id = str(uuid.uuid4())
        self.machine_configs.append(machine_config)
        self._save_data()
        return machine_config

    def update(self, id: str, machine_config: MachineConfig) -> Optional[MachineConfig]:
        for i, config in enumerate(self.machine_configs):
            if config.id == id:
                machine_config.id = id
                self.machine_configs[i] = machine_config
                self._save_data()
                return machine_config
        return None

    def delete(self, id: str) -> bool:
        for i, config in enumerate(self.machine_configs):
            if config.id == id:
                del self.machine_configs[i]
                self._save_data()
                return True
        return False

    def _seed_initial_data(self):
        # Valores iniciales para los modelos de máquina Vega
        initial_configs = [
            {
                "id": str(uuid.uuid4()),
                "model": "440",
                "price": 120000.0,
                "max_speed": 120.0,
                "ink_price": 35.0,
                "ink_consumption": 0.8
            },
            {
                "id": str(uuid.uuid4()),
                "model": "660",
                "price": 180000.0,
                "max_speed": 100.0,
                "ink_price": 35.0,
                "ink_consumption": 0.8
            },
            {
                "id": str(uuid.uuid4()),
                "model": "880",
                "price": 240000.0,
                "max_speed": 80.0,
                "ink_price": 35.0,
                "ink_consumption": 0.8
            }
        ]
        
        self.machine_configs = [MachineConfig(**config) for config in initial_configs]
