from pydantic import BaseModel, EmailStr, Field
from typing import Optional
from datetime import datetime
import uuid

class EmailRequest(BaseModel):
    email: EmailStr = Field(..., description="Email address to send verification code")

class VerificationRequest(BaseModel):
    email: EmailStr = Field(..., description="Email address")
    code: str = Field(..., min_length=6, max_length=6, description="6-digit verification code")

class AuthResponse(BaseModel):
    success: bool
    message: str
    token: Optional[str] = None
    expires_at: Optional[datetime] = None

class VerificationCode(BaseModel):
    id: str
    email: str
    code: str
    created_at: datetime
    expires_at: datetime
    used: bool = False
    attempts: int = 0
    
    class Config:
        from_attributes = True

class TokenData(BaseModel):
    email: Optional[str] = None
    exp: Optional[datetime] = None

class OAuthTokens(BaseModel):
    access_token: str
    refresh_token: Optional[str] = None
    expires_at: Optional[datetime] = None
