from fastapi import Request, HTTPException, status
from fastapi.security.utils import get_authorization_scheme_param
from services.auth_service import AuthService
from typing import List

class AuthMiddleware:
    def __init__(self, protected_paths: List[str] = None):
        """
        Initialize auth middleware
        
        Args:
            protected_paths: List of path prefixes that require authentication
        """
        self.protected_paths = protected_paths or [
            "/calculate",
            "/paper-rolls",
            "/machine-config"
        ]
        self.auth_service = AuthService()
    
    async def __call__(self, request: Request, call_next):
        """Middleware function to check authentication"""
        
        # Check if the path requires authentication
        path = request.url.path
        requires_auth = any(path.startswith(protected_path) for protected_path in self.protected_paths)
        
        # Skip auth for non-protected paths and auth endpoints
        if not requires_auth or path.startswith("/auth/"):
            response = await call_next(request)
            return response
        
        # Get authorization header
        authorization = request.headers.get("Authorization")
        scheme, token = get_authorization_scheme_param(authorization)
        
        if not authorization or scheme.lower() != "bearer":
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Missing or invalid authorization header",
                headers={"WWW-Authenticate": "Bearer"},
            )
        
        # Verify token
        email = self.auth_service.verify_token(token)
        if email is None:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid or expired token",
                headers={"WWW-Authenticate": "Bearer"},
            )
        
        # Add user info to request state
        request.state.user_email = email
        
        response = await call_next(request)
        return response
