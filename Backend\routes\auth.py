from fastapi import <PERSON><PERSON>outer, HTTPException, Depends, status, Query
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from typing import Optional

from models.auth import EmailRequest, VerificationRequest, AuthResponse
from services.auth_service import AuthService

router = APIRouter(
    prefix="/auth",
    tags=["authentication"],
)

security = HTTPBearer()

def get_auth_service():
    return AuthService()

def get_current_user(
    credentials: HTTPAuthorizationCredentials = Depends(security),
    auth_service: AuthService = Depends(get_auth_service)
) -> str:
    """Dependency to get current authenticated user"""
    token = credentials.credentials
    email = auth_service.verify_token(token)
    if email is None:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid authentication credentials",
            headers={"WWW-Authenticate": "Bearer"},
        )
    return email

@router.post("/send-code", response_model=AuthResponse)
async def send_verification_code(
    request: EmailRequest,
    auth_service: AuthService = Depends(get_auth_service)
):
    """Send verification code to email"""
    try:
        success = auth_service.send_verification_code(request.email)
        
        if success:
            return AuthResponse(
                success=True,
                message="Verification code sent successfully"
            )
        else:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to send verification code"
            )
    except Exception as e:
        print(f"Error in send_verification_code: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error"
        )

@router.post("/verify-code", response_model=AuthResponse)
async def verify_code(
    request: VerificationRequest,
    auth_service: AuthService = Depends(get_auth_service)
):
    """Verify code and return JWT token"""
    try:
        token = auth_service.verify_code(request.email, request.code)
        
        if token:
            return AuthResponse(
                success=True,
                message="Code verified successfully",
                token=token
            )
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid or expired verification code"
            )
    except Exception as e:
        print(f"Error in verify_code: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error"
        )

@router.get("/verify-token")
async def verify_token(
    current_user: str = Depends(get_current_user)
):
    """Verify if token is valid"""
    return {
        "valid": True,
        "email": current_user,
        "message": "Token is valid"
    }

@router.get("/oauth/setup-url")
async def get_oauth_setup_url(
    auth_service: AuthService = Depends(get_auth_service)
):
    """Get OAuth authorization URL for initial setup"""
    try:
        auth_url = auth_service.get_oauth_authorization_url()
        return {
            "authorization_url": auth_url,
            "message": "Visit this URL to authorize the application"
        }
    except Exception as e:
        print(f"Error getting OAuth URL: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to generate OAuth URL"
        )

@router.get("/oauth/callback")
async def oauth_callback(
    code: str = Query(..., description="Authorization code from OAuth provider"),
    auth_service: AuthService = Depends(get_auth_service)
):
    """Handle OAuth callback"""
    try:
        success = auth_service.handle_oauth_callback(code)
        
        if success:
            return {
                "success": True,
                "message": "OAuth setup completed successfully"
            }
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Failed to complete OAuth setup"
            )
    except Exception as e:
        print(f"Error in OAuth callback: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error"
        )
