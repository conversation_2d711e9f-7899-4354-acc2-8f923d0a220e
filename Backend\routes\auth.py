from fastapi import <PERSON><PERSON>outer, HTTPException, Depends, status, Query
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from typing import Optional

from models.auth import EmailRequest, VerificationRequest, AuthResponse
try:
    from services.auth_service import AuthService
except ImportError:
    # Fallback to simple auth service if dependencies are missing
    from services.simple_auth_service import SimpleAuthService as AuthService

router = APIRouter(
    prefix="/auth",
    tags=["authentication"],
)

security = HTTPBearer()

def get_auth_service():
    return AuthService()

def get_current_user(
    credentials: HTTPAuthorizationCredentials = Depends(security),
    auth_service: AuthService = Depends(get_auth_service)
) -> str:
    """Dependency to get current authenticated user"""
    token = credentials.credentials
    email = auth_service.verify_token(token)
    if email is None:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid authentication credentials",
            headers={"WWW-Authenticate": "Bearer"},
        )
    return email

@router.post("/send-code", response_model=AuthResponse)
async def send_verification_code(
    request: EmailRequest,
    auth_service: AuthService = Depends(get_auth_service)
):
    """Send verification code to email"""
    try:
        print(f"Attempting to send code to: {request.email}")
        success = auth_service.send_verification_code(request.email)

        if success:
            print(f"Code sent successfully to: {request.email}")
            return AuthResponse(
                success=True,
                message="Verification code sent successfully"
            )
        else:
            print(f"Failed to send code to: {request.email}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to send verification code"
            )
    except HTTPException:
        # Re-raise HTTP exceptions
        raise
    except Exception as e:
        print(f"Error in send_verification_code: {type(e).__name__}: {e}")
        import traceback
        traceback.print_exc()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Internal server error: {str(e)}"
        )

@router.post("/verify-code", response_model=AuthResponse)
async def verify_code(
    request: VerificationRequest,
    auth_service: AuthService = Depends(get_auth_service)
):
    """Verify code and return JWT token"""
    try:
        print(f"Attempting to verify code for email: {request.email}")
        token = auth_service.verify_code(request.email, request.code)

        if token:
            print(f"Code verified successfully for: {request.email}")
            return AuthResponse(
                success=True,
                message="Code verified successfully",
                token=token
            )
        else:
            print(f"Invalid code for email: {request.email}")
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid or expired verification code"
            )
    except HTTPException:
        # Re-raise HTTP exceptions
        raise
    except Exception as e:
        print(f"Error in verify_code: {type(e).__name__}: {e}")
        import traceback
        traceback.print_exc()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Internal server error: {str(e)}"
        )

@router.get("/verify-token")
async def verify_token(
    current_user: str = Depends(get_current_user)
):
    """Verify if token is valid"""
    return {
        "valid": True,
        "email": current_user,
        "message": "Token is valid"
    }

@router.get("/oauth/setup-url")
async def get_oauth_setup_url(
    auth_service: AuthService = Depends(get_auth_service)
):
    """Get OAuth authorization URL for initial setup"""
    try:
        auth_url = auth_service.get_oauth_authorization_url()
        return {
            "authorization_url": auth_url,
            "message": "Visit this URL to authorize the application"
        }
    except Exception as e:
        print(f"Error getting OAuth URL: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to generate OAuth URL"
        )

@router.get("/oauth/callback")
async def oauth_callback(
    code: str = Query(..., description="Authorization code from OAuth provider"),
    auth_service: AuthService = Depends(get_auth_service)
):
    """Handle OAuth callback"""
    try:
        success = auth_service.handle_oauth_callback(code)
        
        if success:
            return {
                "success": True,
                "message": "OAuth setup completed successfully"
            }
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Failed to complete OAuth setup"
            )
    except Exception as e:
        print(f"Error in OAuth callback: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error"
        )
