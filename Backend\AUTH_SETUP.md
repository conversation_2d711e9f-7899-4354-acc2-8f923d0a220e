# Configuración del Sistema de Autenticación

Este documento explica cómo configurar el sistema de autenticación por email con códigos de 6 dígitos usando Gmail OAuth 2.0.

## Configuración Inicial

### 1. Configurar OAuth 2.0 en Google Cloud Console

1. Ve a [Google Cloud Console](https://console.cloud.google.com/)
2. Crea un nuevo proyecto o selecciona uno existente
3. Habilita la Gmail API:
   - Ve a "APIs & Services" > "Library"
   - Busca "Gmail API" y habilítala
4. Configura la pantalla de consentimiento OAuth:
   - Ve a "APIs & Services" > "OAuth consent screen"
   - Configura la información básica de tu aplicación
5. Crea credenciales OAuth 2.0:
   - Ve a "APIs & Services" > "Credentials"
   - Haz clic en "Create Credentials" > "OAuth 2.0 Client IDs"
   - Tipo de aplicación: "Web application"
   - URIs de redirección autorizados: `https://atexco.triky.app/rest/oauth2-credential/callback`

### 2. Configurar el archivo de configuración

1. Abre el archivo `Backend/config/email_config.json`
2. Reemplaza `REPLACE_WITH_YOUR_CLIENT_SECRET` con tu client secret real de Google Cloud Console
3. Verifica que el `client_id` coincida con el tuyo

```json
{
  "oauth2": {
    "client_id": "*********************************************.apps.googleusercontent.com",
    "client_secret": "TU_CLIENT_SECRET_AQUI",
    "redirect_uri": "https://atexco.triky.app/rest/oauth2-credential/callback",
    "scopes": [
      "https://www.googleapis.com/auth/gmail.send"
    ],
    "token_uri": "https://oauth2.googleapis.com/token",
    "auth_uri": "https://accounts.google.com/o/oauth2/auth"
  },
  "email": {
    "from_email": "<EMAIL>",
    "from_name": "Atexco Calculator"
  },
  "verification": {
    "code_length": 6,
    "expiry_minutes": 10,
    "max_attempts": 3
  }
}
```

### 3. Autorizar la aplicación

1. Instala las dependencias:
   ```bash
   cd Backend
   pip install -r requirements.txt
   ```

2. Inicia el servidor:
   ```bash
   python main.py
   ```

3. Obtén la URL de autorización:
   ```bash
   curl http://localhost:3006/auth/oauth/setup-url
   ```

4. Visita la URL devuelta en tu navegador
5. Autoriza la aplicación con la cuenta `<EMAIL>`
6. Copia el código de autorización de la URL de callback
7. Completa la configuración:
   ```bash
   curl "http://localhost:3006/auth/oauth/callback?code=TU_CODIGO_DE_AUTORIZACION"
   ```

## Uso del Sistema

### Endpoints de Autenticación

#### 1. Enviar código de verificación
```bash
POST /auth/send-code
Content-Type: application/json

{
  "email": "<EMAIL>"
}
```

#### 2. Verificar código
```bash
POST /auth/verify-code
Content-Type: application/json

{
  "email": "<EMAIL>",
  "code": "123456"
}
```

#### 3. Verificar token
```bash
GET /auth/verify-token
Authorization: Bearer tu_jwt_token
```

### Flujo de Autenticación

1. **Usuario ingresa email**: El frontend envía el email al endpoint `/auth/send-code`
2. **Sistema envía código**: Se genera un código de 6 dígitos y se envía por email
3. **Usuario ingresa código**: El frontend envía el código al endpoint `/auth/verify-code`
4. **Sistema valida**: Si el código es correcto, se devuelve un JWT token
5. **Acceso autorizado**: El token se incluye en todas las peticiones posteriores

### Configuración de Seguridad

- Los códigos expiran en 10 minutos
- Máximo 3 intentos por código
- Los tokens JWT expiran en 24 horas
- Solo se permite un código activo por email

### Archivos de Datos

- `data/verification_codes.json`: Almacena códigos de verificación temporales
- `data/oauth_tokens.json`: Almacena tokens OAuth para envío de emails

### Variables de Entorno (Opcionales)

Puedes configurar estas variables de entorno para mayor seguridad:

```bash
SECRET_KEY=tu-clave-secreta-jwt
```

## Troubleshooting

### Error: "No OAuth tokens found"
- Asegúrate de haber completado el proceso de autorización OAuth
- Verifica que el archivo `data/oauth_tokens.json` existe y contiene tokens válidos

### Error: "Failed to send email"
- Verifica que el client_secret en `config/email_config.json` es correcto
- Asegúrate de que la cuenta `<EMAIL>` tiene permisos para enviar emails
- Revisa que la Gmail API está habilitada en Google Cloud Console

### Error: "Invalid verification code"
- Los códigos expiran en 10 minutos
- Solo se permiten 3 intentos por código
- Verifica que el email coincide exactamente

## Modo Desarrollo

En modo desarrollo, si el envío de email falla, el código se imprime en la consola del servidor para facilitar las pruebas.
