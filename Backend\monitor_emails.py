#!/usr/bin/env python3
"""
Monitor de emails en tiempo real
"""

import json
import os
import time
from datetime import datetime
from watchdog.observers import Observer
from watchdog.events import FileSystemEventHandler

class EmailMonitor(FileSystemEventHandler):
    def __init__(self):
        self.last_codes = set()
        self.load_existing_codes()
    
    def load_existing_codes(self):
        """Cargar códigos existentes"""
        try:
            if os.path.exists('data/verification_codes.json'):
                with open('data/verification_codes.json', 'r') as f:
                    codes = json.load(f)
                for code_data in codes:
                    self.last_codes.add(code_data.get('id', ''))
        except:
            pass
    
    def on_modified(self, event):
        if event.src_path.endswith('verification_codes.json'):
            self.check_new_codes()
    
    def check_new_codes(self):
        """Verificar nuevos códigos"""
        try:
            with open('data/verification_codes.json', 'r') as f:
                codes = json.load(f)
            
            for code_data in codes:
                code_id = code_data.get('id', '')
                if code_id not in self.last_codes:
                    self.last_codes.add(code_id)
                    self.display_new_code(code_data)
        except Exception as e:
            print(f"Error monitoring codes: {e}")
    
    def display_new_code(self, code_data):
        """Mostrar nuevo código"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        print(f"\n[{timestamp}] 🔔 NUEVO CÓDIGO GENERADO")
        print(f"   📧 Email: {code_data.get('email', 'N/A')}")
        print(f"   🔢 Código: {code_data.get('code', 'N/A')}")
        print(f"   ⏰ Expira: {code_data.get('expires_at', 'N/A')}")
        print(f"   🆔 ID: {code_data.get('id', 'N/A')}")
        print("-" * 50)

def monitor_oauth_tokens():
    """Verificar estado de tokens OAuth"""
    try:
        if os.path.exists('data/oauth_tokens.json'):
            with open('data/oauth_tokens.json', 'r') as f:
                tokens = json.load(f)
            
            if tokens and tokens.get('access_token'):
                print("✅ Tokens OAuth disponibles")
                if 'expires_at' in tokens:
                    print(f"   ⏰ Expiran: {tokens['expires_at']}")
                return True
            else:
                print("❌ Tokens OAuth no válidos")
                return False
        else:
            print("❌ No se encontraron tokens OAuth")
            return False
    except Exception as e:
        print(f"❌ Error verificando tokens: {e}")
        return False

def test_auth_service():
    """Probar servicio de autenticación"""
    try:
        from services.auth_service import AuthService
        
        auth_service = AuthService()
        print("✅ AuthService cargado correctamente")
        
        # Verificar configuración
        config = auth_service.config
        oauth_config = config.get('oauth2', {})
        print(f"   📧 Email configurado: {config.get('email', {}).get('from_email', 'N/A')}")
        print(f"   🔑 Client ID: {oauth_config.get('client_id', 'N/A')[:20]}...")
        
        return True
    except Exception as e:
        print(f"❌ Error cargando AuthService: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    print("🔍 Monitor de Emails - Atexco Calculator")
    print("=" * 50)
    
    # Verificar estado inicial
    print("\n📋 Estado inicial:")
    oauth_ok = monitor_oauth_tokens()
    service_ok = test_auth_service()
    
    if not oauth_ok:
        print("\n⚠️  ADVERTENCIA: OAuth no configurado correctamente")
        print("   Los emails se mostrarán solo en consola (modo desarrollo)")
    
    if not service_ok:
        print("\n❌ ERROR: Servicio de autenticación no funciona")
        return
    
    print(f"\n🔄 Monitoreando emails...")
    print("   📁 Archivo: data/verification_codes.json")
    print("   🔔 Se mostrarán nuevos códigos automáticamente")
    print("   ⏹️  Presiona Ctrl+C para salir")
    print("-" * 50)
    
    # Configurar monitor de archivos
    event_handler = EmailMonitor()
    observer = Observer()
    observer.schedule(event_handler, path='data', recursive=False)
    observer.start()
    
    try:
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        print("\n\n🛑 Monitor detenido")
        observer.stop()
    
    observer.join()

if __name__ == "__main__":
    main()
