#!/usr/bin/env python3
"""
Script para verificar y preparar la configuración OAuth para producción
"""

import json
import os
from datetime import datetime

def check_oauth_config():
    """Verificar la configuración OAuth local"""
    print("=== Verificación de Configuración OAuth ===\n")
    
    # Verificar archivo de configuración
    config_file = "config/email_config.json"
    if not os.path.exists(config_file):
        print("❌ No se encontró config/email_config.json")
        return False
    
    try:
        with open(config_file, 'r') as f:
            config = json.load(f)
        
        oauth_config = config.get("oauth2", {})
        client_secret = oauth_config.get("client_secret", "")
        
        if client_secret == "REPLACE_WITH_YOUR_CLIENT_SECRET":
            print("❌ Client secret no configurado")
            return False
        
        print("✅ Archivo de configuración válido")
        print(f"   Client ID: {oauth_config.get('client_id', 'N/A')}")
        print(f"   Redirect URI: {oauth_config.get('redirect_uri', 'N/A')}")
        
    except Exception as e:
        print(f"❌ Error leyendo configuración: {e}")
        return False
    
    # Verificar tokens OAuth
    tokens_file = "data/oauth_tokens.json"
    if not os.path.exists(tokens_file):
        print("❌ No se encontraron tokens OAuth")
        print("   Ejecuta: python quick_oauth_setup.py")
        return False
    
    try:
        with open(tokens_file, 'r') as f:
            tokens = json.load(f)
        
        if not tokens or not tokens.get("access_token"):
            print("❌ Tokens OAuth vacíos o inválidos")
            return False
        
        print("✅ Tokens OAuth encontrados")
        
        # Verificar expiración
        if "expires_at" in tokens:
            try:
                expires_at = datetime.fromisoformat(tokens["expires_at"])
                now = datetime.now()
                if expires_at <= now:
                    print("⚠️  Tokens OAuth expirados")
                else:
                    print(f"✅ Tokens válidos hasta: {expires_at}")
            except:
                print("⚠️  No se pudo verificar expiración de tokens")
        
    except Exception as e:
        print(f"❌ Error leyendo tokens: {e}")
        return False
    
    return True

def create_production_config():
    """Crear configuración para producción"""
    print("\n=== Configuración para Producción ===\n")
    
    # Leer configuración local
    with open("config/email_config.json", 'r') as f:
        config = json.load(f)
    
    # Actualizar para producción
    config["oauth2"]["redirect_uri"] = "https://atexco.triky.app/rest/oauth2-credential/callback"
    
    # Guardar configuración de producción
    prod_config_file = "config/email_config_production.json"
    with open(prod_config_file, 'w') as f:
        json.dump(config, f, indent=2)
    
    print(f"✅ Configuración de producción creada: {prod_config_file}")
    
    # Mostrar instrucciones
    print("\n📋 Para desplegar en producción:")
    print("1. Copia estos archivos al servidor:")
    print(f"   - {prod_config_file} → config/email_config.json")
    print("   - data/oauth_tokens.json")
    print()
    print("2. Asegúrate de que el servidor use AuthService (no SimpleAuthService)")
    print()
    print("3. Verifica que la URI de redirección en Google Cloud Console incluya:")
    print("   https://atexco.triky.app/rest/oauth2-credential/callback")
    
    return True

def test_oauth_service():
    """Probar el servicio OAuth"""
    print("\n=== Prueba del Servicio OAuth ===\n")
    
    try:
        from services.auth_service import AuthService
        
        auth_service = AuthService()
        print("✅ AuthService importado correctamente")
        
        # Verificar que puede cargar tokens
        tokens = auth_service._load_oauth_tokens()
        if tokens and tokens.get("access_token"):
            print("✅ Tokens OAuth cargados correctamente")
        else:
            print("❌ No se pudieron cargar tokens OAuth")
            return False
        
        # Probar envío de email (sin enviar realmente)
        print("✅ Servicio OAuth listo para usar")
        return True
        
    except Exception as e:
        print(f"❌ Error en servicio OAuth: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    print("🔧 Preparación de OAuth para Producción\n")
    
    # Verificar configuración
    if not check_oauth_config():
        print("\n❌ Configuración OAuth incompleta")
        print("Ejecuta primero: python quick_oauth_setup.py")
        return False
    
    # Crear configuración de producción
    if not create_production_config():
        print("\n❌ Error creando configuración de producción")
        return False
    
    # Probar servicio
    if not test_oauth_service():
        print("\n❌ Error en servicio OAuth")
        return False
    
    print("\n🎉 ¡Configuración OAuth lista para producción!")
    print("\n📤 Archivos a subir al servidor:")
    print("   - config/email_config_production.json → config/email_config.json")
    print("   - data/oauth_tokens.json")
    print("   - routes/auth.py (actualizado)")
    
    return True

if __name__ == "__main__":
    success = main()
    
    if not success:
        print("\n❌ Hay problemas que resolver antes del despliegue")
    
    input("\nPresiona Enter para continuar...")
