import axios from 'axios';
import { PaperRoll, PaperRollCreate, PaperRollUpdate } from '../models/PaperRoll';

import { API_URL } from '../config';

export const paperRollService = {
  async getAll(): Promise<PaperRoll[]> {
    const response = await axios.get(`${API_URL}/paper-rolls`);
    return response.data;
  },

  async getById(id: string): Promise<PaperRoll> {
    const response = await axios.get(`${API_URL}/paper-rolls/${id}`);
    return response.data;
  },

  async create(paperRoll: PaperRollCreate): Promise<PaperRoll> {
    const response = await axios.post(`${API_URL}/paper-rolls`, paperRoll);
    return response.data;
  },

  async update(id: string, paperRoll: PaperRollUpdate): Promise<PaperRoll> {
    const response = await axios.put(`${API_URL}/paper-rolls/${id}`, paperRoll);
    return response.data;
  },

  async delete(id: string): Promise<void> {
    await axios.delete(`${API_URL}/paper-rolls/${id}`);
  }
  // Removed seedInitialData method as we're now using the data file directly
};
