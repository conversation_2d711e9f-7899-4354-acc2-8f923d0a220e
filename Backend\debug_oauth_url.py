#!/usr/bin/env python3
"""
Script para debuggear la URL de OAuth y verificar la configuración
"""

from services.auth_service import AuthService
import json

def debug_oauth_config():
    print("=== Debug OAuth Configuration ===\n")
    
    try:
        # Cargar configuración
        with open('config/email_config.json', 'r') as f:
            config = json.load(f)
        
        print("📋 Configuración actual:")
        oauth_config = config['oauth2']
        print(f"   Client ID: {oauth_config['client_id']}")
        print(f"   Redirect URI: {oauth_config['redirect_uri']}")
        print(f"   Scopes: {oauth_config['scopes']}")
        print()
        
        # Crear servicio de auth
        auth_service = AuthService()
        
        # Generar URL de autorización
        auth_url = auth_service.get_oauth_authorization_url()
        
        print("🔗 URL de autorización generada:")
        print(f"   {auth_url}")
        print()
        
        # Parsear la URL para verificar parámetros
        from urllib.parse import urlparse, parse_qs
        parsed = urlparse(auth_url)
        params = parse_qs(parsed.query)
        
        print("🔍 Parámetros de la URL:")
        for key, value in params.items():
            print(f"   {key}: {value[0] if value else 'None'}")
        print()
        
        # Verificaciones
        print("✅ Verificaciones:")
        
        # Verificar client_id
        expected_client_id = "469796518193-tl497jne98oillhg05o5sv4gjf2hogdr.apps.googleusercontent.com"
        actual_client_id = params.get('client_id', [''])[0]
        if actual_client_id == expected_client_id:
            print("   ✅ Client ID correcto")
        else:
            print(f"   ❌ Client ID incorrecto: {actual_client_id}")
        
        # Verificar redirect_uri
        expected_redirect = "http://localhost:3006/auth/oauth/callback"
        actual_redirect = params.get('redirect_uri', [''])[0]
        if actual_redirect == expected_redirect:
            print("   ✅ Redirect URI correcto")
        else:
            print(f"   ❌ Redirect URI incorrecto: {actual_redirect}")
        
        # Verificar scope
        expected_scope = "https://www.googleapis.com/auth/gmail.send"
        actual_scope = params.get('scope', [''])[0]
        if expected_scope in actual_scope:
            print("   ✅ Scope correcto")
        else:
            print(f"   ❌ Scope incorrecto: {actual_scope}")
        
        print("\n📝 Para configurar en Google Cloud Console:")
        print("   1. Ve a https://console.cloud.google.com/")
        print("   2. APIs & Services > Credentials")
        print(f"   3. Edita el OAuth 2.0 Client ID: {expected_client_id}")
        print("   4. En 'Authorized redirect URIs', asegúrate de tener:")
        print(f"      - {expected_redirect}")
        print("   5. Guarda los cambios")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    debug_oauth_config()
    input("\nPresiona Enter para continuar...")
